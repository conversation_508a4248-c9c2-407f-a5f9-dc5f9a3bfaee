import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { useState, lazy, Suspense } from "react";
import Layout from "@/components/layout/Layout";
import Login from "@/components/auth/Login";
import Dashboard from "@/pages/Dashboard";
import { Toaster } from "sonner";

const Users = lazy(() => import("@/pages/Users"));
const AccessRequests = lazy(() => import("@/pages/AccessRequests"));
const Groups = lazy(() => import("@/pages/Groups"));
const Roles = lazy(() => import("@/pages/Roles"));
const RoleManagement = lazy(() => import("@/pages/RoleManagement"));
const RoleApplicationAssignment = lazy(() => import("@/pages/RoleApplicationAssignment"));
const PropertyGroups = lazy(() => import("@/pages/PropertyGroups"));
const PageManagement = lazy(() => import("@/pages/PageManagement"));
const AuditLogs = lazy(() => import("@/pages/AuditLogs"));

const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="text-center">
      <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <p className="mt-2 text-sm text-slate-600">Loading...</p>
    </div>
  </div>
);

function App() {
  const [isAuthenticated] = useState(true); // For demo purposes, set to true

  if (!isAuthenticated) {
    return <Login />;
  }

  return (
    <Router>
      <Layout>
        <Suspense fallback={<LoadingFallback />}>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/users" element={<Users />} />
            <Route path="/access-requests" element={<AccessRequests />} />
            <Route path="/groups" element={<Groups />} />
            <Route path="/role-management" element={<RoleManagement />} />
            <Route path="/roles" element={<RoleApplicationAssignment />} />
            <Route path="/roles-old" element={<Roles />} />
            <Route path="/property-groups" element={<PropertyGroups />} />
            <Route path="/page-management" element={<PageManagement />} />
            <Route path="/audit-logs" element={<AuditLogs />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
      </Layout>
      <Toaster position="top-right" richColors />
    </Router>
  );
}

export default App;
