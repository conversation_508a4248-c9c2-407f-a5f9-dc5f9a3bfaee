interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'approved' | 'denied';
  className?: string;
}

export default function StatusBadge({ status, className = '' }: StatusBadgeProps) {
  const statusConfig = {
    active: {
      label: 'Active',
      classes: 'bg-green-100 text-green-800 border-green-200'
    },
    inactive: {
      label: 'Inactive', 
      classes: 'bg-red-100 text-red-800 border-red-200'
    },
    pending: {
      label: 'Pending',
      classes: 'bg-yellow-100 text-yellow-800 border-yellow-200'
    },
    approved: {
      label: 'Approved',
      classes: 'bg-green-100 text-green-800 border-green-200'
    },
    denied: {
      label: 'Denied',
      classes: 'bg-red-100 text-red-800 border-red-200'
    }
  };

  const config = statusConfig[status];

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.classes} ${className}`}>
      {config.label}
    </span>
  );
}