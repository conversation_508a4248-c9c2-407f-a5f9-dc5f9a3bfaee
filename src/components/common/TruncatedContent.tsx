import React, { useState, useRef, useEffect } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface TruncatedContentProps {
  children: React.ReactNode;
  maxHeight?: number;
  showMoreText?: string;
  className?: string;
}

export default function TruncatedContent({ children, maxHeight = 40, showMoreText = "...", className = "" }: TruncatedContentProps) {
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (contentRef.current) {
        const element = contentRef.current;
        setIsOverflowing(element.scrollHeight > maxHeight);
      }
    };

    checkOverflow();

    // Recheck on window resize
    window.addEventListener("resize", checkOverflow);
    return () => window.removeEventListener("resize", checkOverflow);
  }, [children, maxHeight]);

  const handleMouseEnter = () => {
    if (isOverflowing) {
      setIsOpen(true);
    }
  };

  const handleMouseLeave = () => {
    setIsOpen(false);
  };

  if (!isOverflowing) {
    return (
      <div ref={contentRef} className={className} style={{ maxHeight: `${maxHeight}px` }}>
        {children}
      </div>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div
          ref={contentRef}
          className={`cursor-pointer ${className}`}
          style={{
            maxHeight: `${maxHeight}px`,
            overflow: "hidden",
            position: "relative",
            paddingRight: "20px", // Add padding to prevent overlap
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div
            style={{
              maxHeight: `${maxHeight}px`,
              overflow: "hidden",
            }}
          >
            {children}
          </div>
          <div className="absolute top-0 right-0 bg-white px-1 text-xs text-wb-gray-500 font-medium">{showMoreText}</div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        className="w-80 max-h-60 overflow-y-auto p-3"
        side="top"
        align="start"
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={handleMouseLeave}
      >
        <div className="space-y-2">{children}</div>
      </PopoverContent>
    </Popover>
  );
}
