import React, { useState, useRef, useEffect } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Plus } from "lucide-react";

interface TruncatedContentProps {
  children: React.ReactNode;
  maxHeight?: number;
  className?: string;
}

export default function TruncatedContent({ children, maxHeight = 40, className = "" }: TruncatedContentProps) {
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (contentRef.current) {
        const element = contentRef.current;
        // Check both horizontal and vertical overflow
        const isHorizontalOverflow = element.scrollWidth > element.clientWidth;
        const isVerticalOverflow = element.scrollHeight > maxHeight;
        setIsOverflowing(isHorizontalOverflow || isVerticalOverflow);
      }
    };

    checkOverflow();

    // Recheck on window resize
    window.addEventListener("resize", checkOverflow);
    return () => window.removeEventListener("resize", checkOverflow);
  }, [children, maxHeight]);

  const handleMouseEnter = () => {
    if (isOverflowing) {
      setIsOpen(true);
    }
  };

  const handleMouseLeave = () => {
    setIsOpen(false);
  };

  if (!isOverflowing) {
    return (
      <div ref={contentRef} className={className} style={{ maxHeight: `${maxHeight}px` }}>
        {children}
      </div>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div
          ref={contentRef}
          className={`cursor-pointer relative ${className}`}
          style={{
            maxHeight: `${maxHeight}px`,
            overflow: "hidden",
            paddingRight: "20px", // Add padding to prevent overlap
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {children}
          <div className="absolute top-0 right-0 bg-white px-1 text-xs text-wb-gray-500 font-medium leading-none flex items-center">
            <Plus className="w-3 h-3" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        className="w-80 max-h-60 overflow-y-auto p-3"
        side="top"
        align="start"
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={handleMouseLeave}
      >
        <div className="space-y-2">{children}</div>
      </PopoverContent>
    </Popover>
  );
}
