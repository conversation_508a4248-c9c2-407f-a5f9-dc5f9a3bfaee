import { Clock, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import Avatar from '../common/Avatar';
import Button from '../common/Button';
import { useAppSelector } from '@/store/hooks';
import { selectPendingRequests } from '@/store/slices/accessRequestsSlice';
import { selectPendingUsers } from '@/store/slices/usersSlice';

export default function PendingRequestsWidget() {
  const pendingRequests = useAppSelector(selectPendingRequests);
  const pendingUsers = useAppSelector(selectPendingUsers);

  return (
    <div className="wb-card">
      <div className="p-6 border-b border-wb-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5 text-wb-accent-500" />
            <h2 className="text-lg font-semibold text-wb-gray-900">Pending Requests</h2>
          </div>
          <span className="bg-wb-accent-100 text-wb-accent-700 text-sm font-medium px-2.5 py-0.5 rounded-full">
            {pendingRequests.length}
          </span>
        </div>
      </div>

      <div className="p-6">
        {pendingRequests.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-wb-gray-300 mx-auto mb-4" />
            <p className="text-wb-gray-500">No pending requests</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              <p className="text-sm text-wb-gray-600">Recent requests awaiting approval:</p>
            </div>
            
            <div className="flex items-center space-x-2 mb-4">
              {pendingUsers.slice(0, 5).map((user, index) => (
                <div
                  key={user.id}
                  className="relative"
                  style={{ marginLeft: index > 0 ? '-8px' : '0' }}
                >
                  <Avatar 
                    name={user.name} 
                    size="sm" 
                    className="border-2 border-white shadow-sm"
                  />
                </div>
              ))}
              {pendingUsers.length > 5 && (
                <div className="w-8 h-8 bg-wb-gray-200 text-wb-gray-600 rounded-full flex items-center justify-center text-xs font-medium border-2 border-white shadow-sm -ml-2">
                  +{pendingUsers.length - 5}
                </div>
              )}
            </div>

            <div className="space-y-3">
              {pendingRequests.slice(0, 3).map(request => (
                <div key={request.id} className="flex items-center justify-between p-3 bg-wb-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Avatar name={request.userName} size="sm" />
                    <div>
                      <p className="text-sm font-medium text-wb-gray-900">{request.userName}</p>
                      <p className="text-xs text-wb-gray-500">{request.requestedRole}</p>
                    </div>
                  </div>
                  <div className="text-xs text-wb-gray-500">
                    {new Date(request.requestDate).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex space-x-3 pt-4">
              <Button size="sm" variant="primary" className="flex-1">
                Bulk Approve
              </Button>
              <Link to="/access-requests" className="flex-1">
                <Button size="sm" variant="secondary" className="w-full">
                  View All
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}