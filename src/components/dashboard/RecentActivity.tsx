import { Activity, CheckCircle, XCircle, UserPlus } from "lucide-react";
import Avatar from "../common/Avatar";
import { useAppSelector } from "@/store/hooks";
import { selectRecentLogs } from "@/store/slices/auditLogsSlice";

interface ActivityItem {
  id: string;
  type: "approval" | "denial" | "user_added" | "role_change";
  user: string;
  description: string;
  timestamp: string;
  actor: string;
}

const mockActivity: ActivityItem[] = [
  {
    id: "1",
    type: "approval",
    user: "<PERSON>",
    description: "Access request approved for pm_user role",
    timestamp: "2 hours ago",
    actor: "Admin User",
  },
  {
    id: "2",
    type: "user_added",
    user: "<PERSON>",
    description: "Added to Regional Manager group",
    timestamp: "4 hours ago",
    actor: "Admin User",
  },
  {
    id: "3",
    type: "denial",
    user: "<PERSON>",
    description: "Access request denied - inactive status",
    timestamp: "6 hours ago",
    actor: "Admin User",
  },
  {
    id: "4",
    type: "approval",
    user: "<PERSON>",
    description: "Financial Suite access granted",
    timestamp: "1 day ago",
    actor: "Admin User",
  },
  {
    id: "5",
    type: "role_change",
    user: "<PERSON>",
    description: "Role updated to Property Manager",
    timestamp: "1 day ago",
    actor: "Admin User",
  },
];

export default function RecentActivity() {
  useAppSelector(selectRecentLogs(5));
  const getIcon = (type: ActivityItem["type"]) => {
    switch (type) {
      case "approval":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "denial":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "user_added":
        return <UserPlus className="w-4 h-4 text-blue-500" />;
      case "role_change":
        return <Activity className="w-4 h-4 text-wb-accent-500" />;
      default:
        return <Activity className="w-4 h-4 text-wb-gray-500" />;
    }
  };

  return (
    <div className="wb-card">
      <div className="p-6 border-b border-wb-gray-200">
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-primary" />
          <h2 className="text-lg font-semibold text-wb-gray-900">Recent Activity</h2>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-4">
          {mockActivity.map((item) => (
            <div key={item.id} className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">{getIcon(item.type)}</div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <Avatar name={item.user} size="sm" />
                  <div className="flex-1">
                    <p className="text-sm text-wb-gray-900">
                      <span className="font-medium">{item.user}</span>
                      <span className="text-wb-gray-600 ml-1">- {item.description}</span>
                    </p>
                    <p className="text-xs text-wb-gray-500">
                      by {item.actor} • {item.timestamp}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 pt-4 border-t border-wb-gray-200">
          <button className="text-sm text-primary hover:text-primary/90 font-medium">View all activity →</button>
        </div>
      </div>
    </div>
  );
}
