import { type ReactNode } from "react";

interface StatsCardProps {
  title: string;
  value: number;
  icon: ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export default function StatsCard({ title, value, icon, trend, className = "" }: StatsCardProps) {
  return (
    <div className={`wb-card p-6 ${className}`}>
      <div className="flex items-center">
        <div className="flex-1">
          <p className="text-sm font-medium text-wb-gray-600">{title}</p>
          <p className="text-3xl font-bold text-wb-gray-900 mt-2">{value.toLocaleString()}</p>
          {trend && (
            <div className="flex items-center mt-2">
              <span className={`text-sm font-medium ${trend.isPositive ? "text-green-600" : "text-red-600"}`}>
                {trend.isPositive ? "+" : ""}
                {trend.value}%
              </span>
              <span className="text-sm text-wb-gray-500 ml-1">from last month</span>
            </div>
          )}
        </div>
        <div className="ml-4 p-3 bg-primary/10 rounded-lg">
          <div className="text-primary">{icon}</div>
        </div>
      </div>
    </div>
  );
}
