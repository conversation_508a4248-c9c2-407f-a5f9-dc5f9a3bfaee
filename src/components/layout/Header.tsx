import { Bell, ChevronDown, User } from 'lucide-react';
import logoLight from '../../assets/images/logo/logo1-light.png';

export default function Header() {
  return (
    <header className="fixed top-0 z-50 w-full bg-[#43298F] h-12">
      <div className="flex items-center justify-between h-full px-4">
        <div className="flex items-center gap-4">
          {/* Logo and Title */}
          <div className="flex items-center gap-3">
            <img
              src={logoLight}
              alt="WillowBridge"
              className="h-7 w-auto object-contain filter brightness-0 invert"
            />
            <div className="flex items-center gap-2">
              <div className="w-[1px] h-5 bg-white/40"></div>
              <h2 className="text-xs font-normal text-white leading-none uppercase tracking-wide">
                Security Administration Portal
              </h2>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Notifications */}
          <button className="relative p-1.5 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
            <Bell className="w-4 h-4" />
            <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-[10px] rounded-full flex items-center justify-center">
              3
            </span>
          </button>

          {/* User Menu */}
          <div className="flex items-center gap-2 px-2 py-1 bg-white/10 rounded-lg cursor-pointer hover:bg-white/20 transition-colors">
            <div className="w-7 h-7 rounded-full border border-white/20 flex items-center justify-center">
              <User className="text-white w-4 h-4" />
            </div>
            <div className="text-left">
              <p className="text-xs font-medium text-white">Sarah Admin</p>
              <p className="text-[10px] text-white/70">Administrator</p>
            </div>
            <ChevronDown className="w-3 h-3 text-white/70" />
          </div>
        </div>
      </div>
    </header>
  );
}