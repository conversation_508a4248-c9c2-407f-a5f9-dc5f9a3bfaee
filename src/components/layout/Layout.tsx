import { type ReactNode } from "react";
import Header from "./Header";
import Sidebar from "./Sidebar";

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  return (
    <div className="h-screen bg-gray-50 overflow-hidden">
      <Header />
      <div className="flex h-full mt-12">
        <Sidebar />
        <main className="flex-1 overflow-auto">
          <div className="p-6 h-full">{children}</div>
        </main>
      </div>
    </div>
  );
}