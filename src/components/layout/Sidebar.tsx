import { useState } from "react";
import { useLocation, Link } from "react-router-dom";
import { LayoutDashboard, Users, UserCheck, UsersIcon, Shield, FileText, Building2, ChevronRight, ChevronDown, Key, Layers } from "lucide-react";

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ElementType;
  path: string;
  children?: SidebarItem[];
}

const sidebarItems: SidebarItem[] = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
    path: "/",
  },
  {
    id: "access-requests",
    label: "Access Requests",
    icon: UserCheck,
    path: "/access-requests",
  },
  {
    id: "users",
    label: "User Management",
    icon: Users,
    path: "/users",
  },
  {
    id: "groups",
    label: "Groups Management",
    icon: UsersIcon,
    path: "/groups",
  },
  {
    id: "property-groups",
    label: "Property Groups",
    icon: Building2,
    path: "/property-groups",
  },
  {
    id: "role-management",
    label: "Role Management",
    icon: Key,
    path: "/role-management",
  },
  {
    id: "roles",
    label: "Role Application Assignment",
    icon: Shield,
    path: "/roles",
  },
  {
    id: "page-management",
    label: "Page Management",
    icon: Layers,
    path: "/page-management",
  },
  {
    id: "audit",
    label: "Audit Logs",
    icon: FileText,
    path: "/audit-logs",
  },
];

export default function Sidebar() {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const location = useLocation();

  const toggleExpanded = (itemId: string) => {
    setExpandedItems((prev) => (prev.includes(itemId) ? prev.filter((id) => id !== itemId) : [...prev, itemId]));
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const renderSidebarItem = (item: SidebarItem) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = isActive(item.path);

    return (
      <div key={item.id} className="mb-0.5">
        <div className="flex items-center">
          <Link
            to={item.path}
            className={`flex-1 flex items-center gap-2.5 px-2.5 py-2 rounded-lg transition-all duration-200 ${
              active ? "bg-[#43298F]/10 text-[#43298F] font-semibold" : "text-gray-700 hover:bg-gray-50 hover:text-[#43298F]"
            }`}
          >
            <item.icon className={`w-4 h-4 ${active ? "text-[#43298F]" : ""}`} />
            <span className="text-[13px]">{item.label}</span>
          </Link>
          {hasChildren && (
            <button onClick={() => toggleExpanded(item.id)} className="p-1 rounded hover:bg-gray-100 ml-1">
              {isExpanded ? <ChevronDown className="w-3 h-3 text-gray-500" /> : <ChevronRight className="w-3 h-3 text-gray-500" />}
            </button>
          )}
        </div>

        {hasChildren && isExpanded && <div className="ml-5 mt-0.5 space-y-0.5">{item.children?.map((child) => renderSidebarItem(child))}</div>}
      </div>
    );
  };

  return (
    <aside className="w-64 bg-white border-r border-gray-200 h-full overflow-y-auto flex-shrink-0">
      <nav className="p-3 space-y-0.5">{sidebarItems.map((item) => renderSidebarItem(item))}</nav>
    </aside>
  );
}
