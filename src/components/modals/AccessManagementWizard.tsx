import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Check, ChevronLeft, ChevronRight, Shield, Users, Building, FolderOpen, AppWindow } from "lucide-react";
import { properties } from "@/data/properties";
import { applicationDisplayNames } from "@/data/roleApplicationMapping";
import type { User } from "@/types";
import { toast } from "sonner";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updateUser } from "@/store/slices/usersSlice";
import { selectAllPropertyGroups } from "@/store/slices/propertyGroupsSlice";
import { selectAllRoles } from "@/store/slices/rolesSlice";
import { selectAllGroups } from "@/store/slices/groupsSlice";
import { selectAllAssignments } from "@/store/slices/roleApplicationAssignmentsSlice";

interface AccessManagementWizardProps {
  open: boolean;
  onClose: () => void;
  user: User;
  onComplete?: () => void;
}

export default function AccessManagementWizard({ open, onClose, user, onComplete }: AccessManagementWizardProps) {
  const dispatch = useAppDispatch();
  const propertyGroups = useAppSelector(selectAllPropertyGroups);
  const roles = useAppSelector(selectAllRoles);
  const groups = useAppSelector(selectAllGroups);
  const assignments = useAppSelector(selectAllAssignments);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [selectedPropertyGroups, setSelectedPropertyGroups] = useState<string[]>([]);

  // Filter roles to only include those with configured assignments
  const configuredRoles = roles.filter(role => {
    return assignments.some(assignment => assignment.roleId === role.id);
  });

  const totalSteps = 3;

  const handleComplete = () => {
    // Get all properties from selected property groups
    const propertiesFromGroups = selectedPropertyGroups.flatMap((groupId) => {
      const group = propertyGroups.find((g) => g.id === groupId);
      return group ? group.properties : [];
    });

    // Combine unique properties from both selections
    const allUniqueProperties = [...new Set([...user.properties, ...selectedProperties, ...propertiesFromGroups])];

    // Combine unique roles (existing + new)
    const allUniqueRoles = [...new Set([...(user.roles || []), ...selectedRoles])];

    // Combine unique groups (existing + new)
    const allUniqueGroups = [...new Set([...(user.groups || []), ...selectedGroups])];

    // Update user with new access
    dispatch(
      updateUser({
        ...user,
        roles: allUniqueRoles,
        groups: allUniqueGroups,
        currentRole: allUniqueRoles[0] || null, // For backward compatibility
        properties: allUniqueProperties,
        propertyGroups: [...(user.propertyGroups || []), ...selectedPropertyGroups],
      })
    );

    // Build success message
    const parts = [];
    if (selectedGroups.length > 0) {
      parts.push(`${selectedGroups.length} group${selectedGroups.length > 1 ? "s" : ""}`);
    }
    if (selectedRoles.length > 0) {
      parts.push(`${selectedRoles.length} role${selectedRoles.length > 1 ? "s" : ""}`);
    }
    if (selectedPropertyGroups.length > 0) {
      parts.push(`${selectedPropertyGroups.length} property group${selectedPropertyGroups.length > 1 ? "s" : ""}`);
    }
    if (selectedProperties.length > 0) {
      parts.push(`${selectedProperties.length} individual propert${selectedProperties.length > 1 ? "ies" : "y"}`);
    }

    toast.success(`Successfully assigned ${parts.join(", ")} to ${user.name}`);

    handleClose();
    if (onComplete) onComplete();
  };

  const handleClose = () => {
    setCurrentStep(1);
    setSelectedRoles([]);
    setSelectedGroups([]);
    setSelectedProperties([]);
    setSelectedPropertyGroups([]);
    onClose();
  };

  const handleNext = () => {
    if (currentStep === 1 && selectedRoles.length === 0 && selectedGroups.length === 0) {
      toast.error("Please select at least one role or group");
      return;
    }
    if (currentStep === 2 && selectedProperties.length === 0 && selectedPropertyGroups.length === 0) {
      toast.error("Please select at least one property or property group");
      return;
    }
    setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
  };

  const handleBack = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const canProceed = () => {
    if (currentStep === 1) {
      return selectedRoles.length > 0 || selectedGroups.length > 0;
    }
    if (currentStep === 2) {
      return selectedProperties.length > 0 || selectedPropertyGroups.length > 0;
    }
    return true;
  };

  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl">Manage Access for {user.name}</DialogTitle>
          <div className="mt-4 space-y-2">
            <Progress value={progressPercentage} className="h-2" />
            <div className="flex justify-between text-sm text-gray-500">
              <span className={currentStep >= 1 ? "text-primary font-medium" : ""}>1. Roles & Groups</span>
              <span className={currentStep >= 2 ? "text-primary font-medium" : ""}>2. Property Selection</span>
              <span className={currentStep >= 3 ? "text-primary font-medium" : ""}>3. Review</span>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-1">
          {/* Step 1: Role and Group Selection */}
          {currentStep === 1 && (
            <div className="space-y-6 py-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Assign Roles & Groups</h3>
                <p className="text-sm text-gray-500 mb-4">Select groups for inherited permissions and/or individual roles for specific access</p>
              </div>

              <Tabs defaultValue="groups" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="groups">Groups</TabsTrigger>
                  <TabsTrigger value="roles">Individual Roles</TabsTrigger>
                </TabsList>

                <TabsContent value="groups" className="mt-4 space-y-3">
                  <div className="text-sm text-gray-600 mb-4">
                    Groups provide a set of permissions that are inherited by all members.
                    {selectedGroups.length > 0 && (
                      <span className="block mt-1 font-medium text-primary">
                        {selectedGroups.length} group{selectedGroups.length > 1 ? "s" : ""} selected
                      </span>
                    )}
                  </div>
                  {groups.map((group) => (
                    <Card
                      key={group.id}
                      className={`p-4 cursor-pointer transition-colors ${
                        selectedGroups.includes(group.id) ? "border-primary bg-primary/5" : "hover:bg-gray-50"
                      }`}
                      onClick={() => {
                        setSelectedGroups((prev) => (prev.includes(group.id) ? prev.filter((id) => id !== group.id) : [...prev, group.id]));
                      }}
                    >
                      <div className="flex items-start">
                        <Checkbox
                          checked={selectedGroups.includes(group.id)}
                          onCheckedChange={(checked) => {
                            setSelectedGroups((prev) => (checked ? [...prev, group.id] : prev.filter((id) => id !== group.id)));
                          }}
                          className="mt-1"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-2 text-primary" />
                            <h5 className="font-medium">{group.name}</h5>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">{group.description}</p>
                          <div className="mt-2 space-y-2">
                            <p className="text-sm text-gray-600">
                              {group.memberCount} member{group.memberCount !== 1 ? "s" : ""}
                            </p>
                            {group.applicationRoles && group.applicationRoles.length > 0 && (
                              <div className="space-y-1">
                                <div className="flex items-center text-xs text-gray-500">
                                  <AppWindow className="w-3 h-3 mr-1" />
                                  Application Roles:
                                </div>
                                {(() => {
                                  const appGroups = group.applicationRoles.reduce((acc, role) => {
                                    const key = role.applicationName || role.applicationId;
                                    if (!acc[key]) acc[key] = [];
                                    acc[key].push(role.roleName);
                                    return acc;
                                  }, {} as Record<string, string[]>);

                                  return Object.entries(appGroups).map(([app, roles]) => (
                                    <div key={app} className="ml-4 text-xs">
                                      <span className="font-medium">{app}:</span> {roles.join(", ")}
                                    </div>
                                  ));
                                })()}
                              </div>
                            )}
                            <div className="flex flex-wrap gap-1">
                              {group.permissions.slice(0, 3).map((permission) => (
                                <Badge key={permission} variant="secondary" className="text-xs">
                                  {permission}
                                </Badge>
                              ))}
                              {group.permissions.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{group.permissions.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </TabsContent>

                <TabsContent value="roles" className="mt-4 space-y-3">
                  <div className="text-sm text-gray-600 mb-4">
                    Individual roles with configured application permissions for granular access control.
                    {selectedRoles.length > 0 && (
                      <span className="block mt-1 font-medium text-primary">
                        {selectedRoles.length} role{selectedRoles.length > 1 ? "s" : ""} selected
                      </span>
                    )}
                  </div>
                  {configuredRoles.length === 0 ? (
                    <Card className="p-6 text-center">
                      <Shield className="w-12 h-12 text-slate-300 mx-auto mb-3" />
                      <p className="text-slate-600">No configured roles available</p>
                      <p className="text-sm text-slate-500 mt-1">Create role-application assignments first</p>
                    </Card>
                  ) : (
                    configuredRoles.map((role) => (
                    <Card
                      key={role.id}
                      className={`p-4 cursor-pointer transition-colors ${
                        selectedRoles.includes(role.id) ? "border-primary bg-primary/5" : "hover:bg-gray-50"
                      }`}
                      onClick={() => {
                        setSelectedRoles((prev) => (prev.includes(role.id) ? prev.filter((id) => id !== role.id) : [...prev, role.id]));
                      }}
                    >
                      <div className="flex items-start">
                        <Checkbox
                          checked={selectedRoles.includes(role.id)}
                          onCheckedChange={(checked) => {
                            setSelectedRoles((prev) => (checked ? [...prev, role.id] : prev.filter((id) => id !== role.id)));
                          }}
                          className="mt-1"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center">
                            <Shield className="w-4 h-4 mr-2 text-primary" />
                            <h5 className="font-medium">{role.name}</h5>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">{role.description}</p>
                          <p className="text-xs text-primary mt-1">
                            {assignments.filter(a => a.roleId === role.id).length} application{assignments.filter(a => a.roleId === role.id).length !== 1 ? 's' : ''} configured
                          </p>
                          {role.applicationPermissions && role.applicationPermissions.length > 0 && (
                            <div className="mt-2 flex items-center flex-wrap gap-2">
                              <AppWindow className="w-3 h-3 text-gray-400" />
                              {role.applicationPermissions.map((appPerm) => (
                                <Badge key={appPerm.applicationId} variant="outline" className="text-xs">
                                  {appPerm.applicationName || applicationDisplayNames[appPerm.applicationId] || appPerm.applicationId}
                                </Badge>
                              ))}
                            </div>
                          )}
                          <div className="mt-2 flex flex-wrap gap-1">
                            {role.permissions.slice(0, 3).map((permission) => (
                              <Badge key={permission} variant="secondary" className="text-xs">
                                {permission}
                              </Badge>
                            ))}
                            {role.permissions.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{role.permissions.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </Card>
                  )))}
                </TabsContent>
              </Tabs>
            </div>
          )}

          {/* Step 2: Property Selection */}
          {currentStep === 2 && (
            <div className="space-y-6 py-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Select Properties</h3>
                <p className="text-sm text-gray-500 mb-4">Choose property groups and/or individual properties {user.name} will have access to</p>
              </div>

              <Tabs defaultValue="groups" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="groups">Property Groups</TabsTrigger>
                  <TabsTrigger value="individual">Individual Properties</TabsTrigger>
                </TabsList>

                <TabsContent value="groups" className="mt-4 space-y-3">
                  <div className="text-sm text-gray-600 mb-4">
                    Property groups provide access to multiple properties at once.
                    {selectedPropertyGroups.length > 0 && (
                      <span className="block mt-1 font-medium text-primary">{selectedPropertyGroups.length} group(s) selected</span>
                    )}
                  </div>
                  {propertyGroups.map((group) => (
                    <Card
                      key={group.id}
                      className={`p-4 cursor-pointer transition-colors ${
                        selectedPropertyGroups.includes(group.id) ? "border-primary bg-primary/5" : "hover:bg-gray-50"
                      }`}
                      onClick={() => {
                        setSelectedPropertyGroups((prev) => (prev.includes(group.id) ? prev.filter((id) => id !== group.id) : [...prev, group.id]));
                      }}
                    >
                      <div className="flex items-start">
                        <Checkbox
                          checked={selectedPropertyGroups.includes(group.id)}
                          onCheckedChange={(checked) => {
                            setSelectedPropertyGroups((prev) => (checked ? [...prev, group.id] : prev.filter((id) => id !== group.id)));
                          }}
                          className="mt-1"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center">
                            <FolderOpen className="w-4 h-4 mr-2 text-primary" />
                            <h5 className="font-medium">{group.name}</h5>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">{group.description}</p>
                          <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
                            <span className="flex items-center">
                              <Building className="w-3 h-3 mr-1" />
                              {group.properties.length} properties
                            </span>
                            <span className="text-xs text-gray-400">
                              {properties
                                .filter((p) => group.properties.includes(p.id))
                                .slice(0, 3)
                                .map((p) => p.name)
                                .join(", ")}
                              {group.properties.length > 3 && ` +${group.properties.length - 3} more`}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </TabsContent>

                <TabsContent value="individual" className="mt-4 space-y-4">
                  <div className="text-sm text-gray-600 mb-4">
                    Select specific properties for granular access control.
                    {selectedProperties.length > 0 && (
                      <span className="block mt-1 font-medium text-primary">
                        {selectedProperties.length} propert{selectedProperties.length === 1 ? "y" : "ies"} selected
                      </span>
                    )}
                  </div>
                  {["East", "West"].map((region) => (
                    <div key={region}>
                      <h4 className="font-medium mb-3 flex items-center">
                        <Building className="w-4 h-4 mr-2" />
                        {region} Region
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {properties
                          .filter((property) => property.region === region)
                          .map((property) => (
                            <Card
                              key={property.id}
                              className={`p-4 cursor-pointer transition-colors ${
                                selectedProperties.includes(property.id) ? "border-primary bg-primary/5" : "hover:bg-gray-50"
                              }`}
                              onClick={() => {
                                setSelectedProperties((prev) =>
                                  prev.includes(property.id) ? prev.filter((id) => id !== property.id) : [...prev, property.id]
                                );
                              }}
                            >
                              <div className="flex items-center">
                                <Checkbox
                                  checked={selectedProperties.includes(property.id)}
                                  onCheckedChange={(checked) => {
                                    setSelectedProperties((prev) => (checked ? [...prev, property.id] : prev.filter((id) => id !== property.id)));
                                  }}
                                />
                                <div className="ml-3">
                                  <h5 className="font-medium">{property.name}</h5>
                                  <p className="text-sm text-gray-500">{property.type}</p>
                                </div>
                              </div>
                            </Card>
                          ))}
                      </div>
                    </div>
                  ))}
                </TabsContent>
              </Tabs>
            </div>
          )}

          {/* Step 3: Review */}
          {currentStep === 3 && (
            <div className="space-y-6 py-4">
              <div>
                <h3 className="text-lg font-medium mb-4">Review Access Assignment</h3>
              </div>

              <Card className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-12 w-12">
                      <AvatarFallback>
                        {user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h4 className="font-medium">{user.name}</h4>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h5 className="font-medium mb-2">Access Assignment</h5>

                    {selectedGroups.length > 0 && (
                      <div className="mb-3">
                        <h6 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          Groups ({selectedGroups.length})
                        </h6>
                        <div className="space-y-2">
                          {selectedGroups.map((groupId) => {
                            const group = groups.find((g) => g.id === groupId);
                            return group ? (
                              <div key={groupId} className="bg-gray-50 rounded p-2">
                                <div className="flex items-center space-x-2">
                                  <Badge variant="secondary">{group.name}</Badge>
                                </div>
                                {group.applicationRoles && group.applicationRoles.length > 0 && (
                                  <div className="mt-1 text-xs text-gray-600">
                                    <AppWindow className="w-3 h-3 inline mr-1" />
                                    {(() => {
                                      const apps = [...new Set(group.applicationRoles.map((r) => r.applicationName || r.applicationId))];
                                      return apps.join(", ");
                                    })()}
                                  </div>
                                )}
                              </div>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}

                    {selectedRoles.length > 0 && (
                      <div>
                        <h6 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <Shield className="w-4 h-4 mr-1" />
                          Individual Roles ({selectedRoles.length})
                        </h6>
                        <div className="space-y-2">
                          {selectedRoles.map((roleId) => {
                            const role = configuredRoles.find((r) => r.id === roleId);
                            return role ? (
                              <div key={roleId} className="bg-gray-50 rounded p-2">
                                <div className="flex items-center space-x-2">
                                  <Badge variant="secondary">{role.name}</Badge>
                                </div>
                                {role.applicationPermissions && role.applicationPermissions.length > 0 && (
                                  <div className="mt-1 text-xs text-gray-600">
                                    <AppWindow className="w-3 h-3 inline mr-1" />
                                    {role.applicationPermissions.map((appPerm) => appPerm.applicationName || appPerm.applicationId).join(", ")}
                                  </div>
                                )}
                              </div>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="border-t pt-4">
                    <h5 className="font-medium mb-2">Property Access</h5>

                    {selectedPropertyGroups.length > 0 && (
                      <div className="mb-4">
                        <h6 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <FolderOpen className="w-4 h-4 mr-1" />
                          Property Groups ({selectedPropertyGroups.length})
                        </h6>
                        <div className="space-y-2">
                          {selectedPropertyGroups.map((groupId) => {
                            const group = propertyGroups.find((g) => g.id === groupId);
                            return group ? (
                              <div key={groupId} className="bg-gray-50 rounded p-2">
                                <div className="font-medium text-sm">{group.name}</div>
                                <div className="text-xs text-gray-500 mt-1">{group.properties.length} properties</div>
                              </div>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}

                    {selectedProperties.length > 0 && (
                      <div>
                        <h6 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <Building className="w-4 h-4 mr-1" />
                          Individual Properties ({selectedProperties.length})
                        </h6>
                        <div className="grid grid-cols-2 gap-2">
                          {selectedProperties.map((propertyId) => {
                            const property = properties.find((p) => p.id === propertyId);
                            return property ? (
                              <div key={propertyId} className="flex items-center space-x-2">
                                <Building className="w-4 h-4 text-gray-400" />
                                <span className="text-sm">{property.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {property.region}
                                </Badge>
                              </div>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Card>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <Check className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h5 className="font-medium text-blue-900">Ready to Apply Changes</h5>
                    <p className="text-sm text-blue-700 mt-1">
                      Click "Apply Changes" to update {user.name}'s access permissions. They will receive an email notification about the changes.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="pt-4 border-t">
          <div className="flex justify-between w-full">
            <Button variant="outline" onClick={currentStep === 1 ? handleClose : handleBack}>
              {currentStep === 1 ? (
                "Cancel"
              ) : (
                <>
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  Back
                </>
              )}
            </Button>
            {currentStep < totalSteps ? (
              <Button onClick={handleNext} disabled={!canProceed()}>
                Next
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button onClick={handleComplete}>Apply Changes</Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
