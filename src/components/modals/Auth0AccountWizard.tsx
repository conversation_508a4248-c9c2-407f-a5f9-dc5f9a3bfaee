import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import { Check, X, UserPlus, Mail } from "lucide-react";
import type { AccessRequest } from "@/types";

interface Auth0AccountWizardProps {
  open: boolean;
  onClose: () => void;
  requests: AccessRequest[];
  onComplete: () => void;
}

export default function Auth0AccountWizard({ open, onClose, requests, onComplete }: Auth0AccountWizardProps) {
  const [approvalStatus, setApprovalStatus] = useState<"pending" | "approved" | "denied">("pending");
  const [rejectionReason, setRejectionReason] = useState("");
  const [auth0Email, setAuth0Email] = useState("");
  const [auth0Password, setAuth0Password] = useState("");
  const [sendEmail, setSendEmail] = useState(true);
  const [emailMessage, setEmailMessage] = useState(
    "Your Auth0 account has been created. An administrator will contact you regarding access permissions."
  );

  const handleComplete = () => {
    if (approvalStatus === "approved") {
      onComplete();
    }
    handleClose();
  };

  const handleClose = () => {
    setApprovalStatus("pending");
    setRejectionReason("");
    setAuth0Email("");
    setAuth0Password("");
    setSendEmail(true);
    setEmailMessage("Your Auth0 account has been created. An administrator will contact you regarding access permissions.");
    onClose();
  };

  const generatePassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setAuth0Password(password);
  };

  if (approvalStatus === "denied") {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Request Denied</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label>Reason for Denial</Label>
              <Textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Please provide a reason for denying this request..."
                className="mt-2 min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalStatus("pending")}>
              Back
            </Button>
            <Button variant="destructive" onClick={handleComplete}>
              Confirm Denial
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Process Access Request</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Request Summary */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Request Summary</h3>
            <div className="space-y-3">
              {requests.map((request) => (
                <Card key={request.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>
                          {request.userName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-medium">{request.userName}</h4>
                        <p className="text-sm text-gray-500">{request.userEmail}</p>
                        <div className="mt-2 space-y-1">
                          <p className="text-sm">
                            <span className="text-gray-500">Requested Role:</span> <Badge variant="outline">{request.requestedRole}</Badge>
                          </p>
                          <p className="text-sm text-gray-500">Reason: {request.reason}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Approval Decision */}
          {approvalStatus === "pending" && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Decision</h3>
              <div className="flex gap-3">
                <Button variant="outline" className="flex-1" onClick={() => setApprovalStatus("approved")}>
                  <Check className="w-4 h-4 mr-2" />
                  Approve & Create Auth0 Account
                </Button>
                <Button variant="destructive" className="flex-1" onClick={() => setApprovalStatus("denied")}>
                  <X className="w-4 h-4 mr-2" />
                  Deny Request
                </Button>
              </div>
            </div>
          )}

          {/* Auth0 Account Setup */}
          {approvalStatus === "approved" && (
            <>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <UserPlus className="w-4 h-4 mr-2" />
                  Auth0 Account Setup
                </h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="auth0-email">Email Address</Label>
                    <Input
                      id="auth0-email"
                      type="email"
                      value={auth0Email || requests[0]?.userEmail || ""}
                      onChange={(e) => setAuth0Email(e.target.value)}
                      placeholder="<EMAIL>"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="auth0-password">Temporary Password</Label>
                    <div className="flex space-x-2 mt-1">
                      <Input
                        id="auth0-password"
                        type="text"
                        value={auth0Password}
                        onChange={(e) => setAuth0Password(e.target.value)}
                        placeholder="Enter temporary password"
                      />
                      <Button variant="outline" onClick={generatePassword}>
                        Generate
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">User will be required to change password on first login</p>
                  </div>
                </div>
              </div>

              {/* Email Notification */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <Mail className="w-4 h-4 mr-2" />
                  Email Notification
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="send-email"
                      checked={sendEmail}
                      onChange={(e) => setSendEmail(e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="send-email">Send welcome email to user</Label>
                  </div>
                  {sendEmail && (
                    <div>
                      <Label htmlFor="email-message">Email Message</Label>
                      <Textarea
                        id="email-message"
                        value={emailMessage}
                        onChange={(e) => setEmailMessage(e.target.value)}
                        className="mt-1 min-h-[100px]"
                      />
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          {approvalStatus === "approved" && <Button onClick={handleComplete}>Create Auth0 Account</Button>}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
