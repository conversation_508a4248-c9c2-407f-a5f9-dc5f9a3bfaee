import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Shield, AppWindow, ChevronRight, ArrowRight } from "lucide-react";
import { applications } from "@/data/applications";
import { applicationPages } from "@/data/permissions";
import type { Role } from "@/types";

interface EnhancedRoleCreationWizardProps {
  open: boolean;
  onClose: () => void;
  editingRole?: Role | null;
  onComplete?: (role: Role) => void;
}

export default function EnhancedRoleCreationWizard({ open, onClose, editingRole }: EnhancedRoleCreationWizardProps) {
  const navigate = useNavigate();

  const handleApplicationSelect = (appId: string) => {
    // Navigate to the new dedicated page with the selected application
    onClose(); // Close the modal first
    navigate(`/roles/assign?app=${appId}`);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            <Shield className="w-5 h-5 text-primary" />
            {editingRole ? "Edit Role" : "Configure Role Permissions"}
          </DialogTitle>
          <p className="text-sm text-slate-600 mt-1">Select an application to configure role permissions and access control.</p>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-slate-900">Select Application</h3>
                <p className="text-sm text-slate-600">Choose which application you want to configure permissions for</p>
              </div>
              <Badge variant="outline" className="text-xs">
                {applications.length} Applications Available
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[400px] overflow-y-auto">
              {applications.map((app) => (
                <Card
                  key={app.id}
                  className="p-4 hover:shadow-md transition-all cursor-pointer border-2 hover:border-primary/20 group"
                  onClick={() => handleApplicationSelect(app.id)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                      <AppWindow className="w-5 h-5 text-primary" />
                    </div>
                    <ArrowRight className="w-4 h-4 text-slate-400 group-hover:text-primary transition-colors" />
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-semibold text-slate-900 group-hover:text-primary transition-colors">{app.name}</h4>
                    <p className="text-sm text-slate-600 line-clamp-2">{app.description}</p>
                    <div className="flex items-center justify-between pt-2">
                      <Badge variant="secondary" className="text-xs">
                        {(applicationPages[app.id] || []).length} pages
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-1 text-primary hover:text-primary"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleApplicationSelect(app.id);
                        }}
                      >
                        Configure <ChevronRight className="w-3 h-3 ml-1" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Info Section */}
          <div className="mt-6 p-4 bg-slate-50 rounded-lg border">
            <div className="flex items-start space-x-3">
              <div className="p-1 bg-blue-100 rounded">
                <Shield className="w-4 h-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-slate-900 mb-1">Role Permission Management</h4>
                <p className="text-xs text-slate-600">After selecting an application, you'll be taken to a dedicated interface where you can:</p>
                <ul className="text-xs text-slate-600 mt-2 space-y-1 ml-4 list-disc">
                  <li>Select multiple roles to configure simultaneously</li>
                  <li>Set granular CRUD permissions for each application page</li>
                  <li>Use bulk operations to speed up configuration</li>
                  <li>Get a clear overview of all permission assignments</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
