import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Users, Shield, ChevronLeft, ChevronRight, Check, AppWindow, X, Key, FileText } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Group } from "@/types";
import { useAppSelector } from "@/store/hooks";
import { selectAllAssignments } from "@/store/slices/roleApplicationAssignmentsSlice";

interface GroupCreationWizardProps {
  open: boolean;
  onClose: () => void;
  editingGroup?: Group | null;
  onComplete: (group: Partial<Group>) => void;
}

export default function GroupCreationWizard({ open, onClose, editingGroup, onComplete }: GroupCreationWizardProps) {
  const assignments = useAppSelector(selectAllAssignments);
  const [currentStep, setCurrentStep] = useState(1);
  const [groupName, setGroupName] = useState("");
  const [groupDescription, setGroupDescription] = useState("");
  const [selectedAssignmentIds, setSelectedAssignmentIds] = useState<string[]>([]);

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  useEffect(() => {
    if (editingGroup) {
      setGroupName(editingGroup.name);
      setGroupDescription(editingGroup.description);
      setSelectedAssignmentIds(editingGroup.assignmentIds || []);
    } else {
      resetForm();
    }
  }, [editingGroup]);

  const resetForm = () => {
    setGroupName("");
    setGroupDescription("");
    setSelectedAssignmentIds([]);
    setCurrentStep(1);
  };

  const handleAssignmentToggle = (assignmentId: string) => {
    setSelectedAssignmentIds((prev) => {
      if (prev.includes(assignmentId)) {
        return prev.filter((id) => id !== assignmentId);
      } else {
        return [...prev, assignmentId];
      }
    });
  };

  const isAssignmentSelected = (assignmentId: string) => {
    return selectedAssignmentIds.includes(assignmentId);
  };

  const getSelectedAssignments = () => {
    return assignments.filter((a) => selectedAssignmentIds.includes(a.id));
  };

  const getPermissionCount = (permissions: { [pageId: string]: string[] }) => {
    let count = 0;
    Object.values(permissions).forEach((perms) => {
      count += perms.length;
    });
    return count;
  };

  const getPageCount = (permissions: { [pageId: string]: string[] }) => {
    return Object.keys(permissions).length;
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    const group: Partial<Group> = {
      id: editingGroup?.id || `group-${Date.now()}`,
      name: groupName,
      description: groupDescription,
      members: editingGroup?.members || [],
      memberCount: editingGroup?.memberCount || 0,
      permissions: [], // Legacy field
      assignmentIds: selectedAssignmentIds,
    };

    onComplete(group);
    resetForm();
  };

  const removeAssignment = (assignmentId: string) => {
    setSelectedAssignmentIds((prev) => prev.filter((id) => id !== assignmentId));
  };

  // Group assignments by application for better display
  const groupedAssignments = assignments.reduce((acc, assignment) => {
    const appName = assignment.applicationName;
    if (!acc[appName]) {
      acc[appName] = [];
    }
    acc[appName].push(assignment);
    return acc;
  }, {} as Record<string, typeof assignments>);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">{editingGroup ? "Edit Group" : "Create New Group"}</DialogTitle>
          <DialogDescription>
            {editingGroup ? "Update the group information and role assignments" : "Create a new group with specific role assignments"}
          </DialogDescription>
          <div className="flex items-center gap-2 mt-2">
            <Progress value={progress} className="flex-1" />
            <span className="text-sm text-slate-500">
              Step {currentStep} of {totalSteps}
            </span>
          </div>
        </DialogHeader>

        <div className="py-6">
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Users className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 1: Group Information</h3>
                  <p className="text-sm text-slate-600">Define the group name and description</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="group-name">Group Name *</Label>
                  <Input
                    id="group-name"
                    value={groupName}
                    onChange={(e) => setGroupName(e.target.value)}
                    placeholder="e.g., Regional Managers"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="group-description">Group Description *</Label>
                  <Textarea
                    id="group-description"
                    value={groupDescription}
                    onChange={(e) => setGroupDescription(e.target.value)}
                    placeholder="Describe the purpose and members of this group..."
                    className="mt-1"
                    rows={4}
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Shield className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 2: Select Role-Application Assignments</h3>
                  <p className="text-sm text-slate-600">Choose pre-configured role assignments for this group</p>
                </div>
              </div>

              <div className="space-y-4">
                {assignments.length === 0 ? (
                  <Card className="p-6 text-center">
                    <FileText className="w-12 h-12 text-slate-300 mx-auto mb-3" />
                    <p className="text-slate-600">No role-application assignments available</p>
                    <p className="text-sm text-slate-500 mt-1">Create assignments in the Role Application Assignment page first</p>
                  </Card>
                ) : (
                  Object.entries(groupedAssignments).map(([appName, appAssignments]) => (
                    <Card key={appName} className="p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <AppWindow className="w-5 h-5 text-primary" />
                        <h4 className="font-medium">{appName}</h4>
                        <Badge variant="secondary" className="ml-auto">
                          {appAssignments.length} assignment{appAssignments.length === 1 ? "" : "s"}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        {appAssignments.map((assignment) => {
                          const permissionCount = getPermissionCount(assignment.permissions);
                          const pageCount = getPageCount(assignment.permissions);
                          
                          return (
                            <div
                              key={assignment.id}
                              className={cn(
                                "flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors",
                                isAssignmentSelected(assignment.id)
                                  ? "bg-primary/5 border-primary/30"
                                  : "hover:bg-slate-50 border-slate-200"
                              )}
                              onClick={() => handleAssignmentToggle(assignment.id)}
                            >
                              <Checkbox
                                checked={isAssignmentSelected(assignment.id)}
                                onCheckedChange={() => handleAssignmentToggle(assignment.id)}
                                onClick={(e) => e.stopPropagation()}
                              />
                              <div className="flex-1">
                                <div className="flex items-start justify-between">
                                  <div>
                                    <div className="flex items-center gap-2">
                                      <Key className="w-4 h-4 text-slate-500" />
                                      <p className="font-medium text-sm">{assignment.roleName}</p>
                                    </div>
                                    <p className="text-xs text-slate-600 mt-1">
                                      {pageCount} pages • {permissionCount} permissions
                                    </p>
                                  </div>
                                  <div className="text-xs text-slate-500">
                                    Modified: {assignment.modifiedAt}
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </Card>
                  ))
                )}

                {selectedAssignmentIds.length > 0 && (
                  <Card className="p-4 bg-primary/5 border-primary/20">
                    <h4 className="font-medium text-sm mb-3">Selected Assignments ({selectedAssignmentIds.length})</h4>
                    <div className="space-y-2">
                      {getSelectedAssignments().map((assignment) => (
                        <div key={assignment.id} className="flex items-center justify-between bg-white rounded-lg p-2">
                          <div className="flex items-center gap-2">
                            <Shield className="w-4 h-4 text-primary" />
                            <span className="text-sm">
                              <span className="font-medium">{assignment.roleName}</span>
                              <span className="text-slate-500"> for </span>
                              <span className="font-medium">{assignment.applicationName}</span>
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeAssignment(assignment.id);
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </Card>
                )}
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Check className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 3: Review & Create</h3>
                  <p className="text-sm text-slate-600">Review the group configuration before creating</p>
                </div>
              </div>

              <Card className="p-4 space-y-4">
                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-2">Group Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-500">Name:</span>
                      <span className="font-medium">{groupName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-500">Description:</span>
                      <span className="font-medium text-right max-w-xs">{groupDescription}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-2">Role-Application Assignments</h4>
                  {selectedAssignmentIds.length > 0 ? (
                    <div className="space-y-2">
                      {Object.entries(groupedAssignments).map(([appName, appAssignments]) => {
                        const selectedAppAssignments = appAssignments.filter((a) => 
                          selectedAssignmentIds.includes(a.id)
                        );
                        
                        if (selectedAppAssignments.length === 0) return null;

                        return (
                          <div key={appName} className="p-3 bg-slate-50 rounded">
                            <div className="flex items-center gap-2 mb-2">
                              <AppWindow className="w-4 h-4 text-primary" />
                              <span className="text-sm font-medium">{appName}</span>
                            </div>
                            {selectedAppAssignments.map((assignment) => {
                              const permissionCount = getPermissionCount(assignment.permissions);
                              const pageCount = getPageCount(assignment.permissions);
                              
                              return (
                                <div key={assignment.id} className="ml-6 py-1">
                                  <div className="flex items-center gap-2">
                                    <Key className="w-3 h-3 text-slate-500" />
                                    <span className="text-sm">{assignment.roleName}</span>
                                    <Badge variant="secondary" className="text-xs">
                                      {pageCount} pages • {permissionCount} permissions
                                    </Badge>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <p className="text-sm text-slate-500">No assignments selected</p>
                  )}
                </div>
              </Card>

              <Card className="p-4 bg-green-50 border-green-200">
                <div className="flex items-start gap-3">
                  <Check className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-sm text-green-900">{editingGroup ? "Ready to update group" : "Ready to create group"}</p>
                    <p className="text-xs text-green-700 mt-1">
                      Click "{editingGroup ? "Update" : "Create"}" to {editingGroup ? "save changes" : "create this group"}.
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleBack} disabled={currentStep === 1}>
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          {currentStep < totalSteps ? (
            <Button onClick={handleNext} disabled={currentStep === 1 && (!groupName || !groupDescription)}>
              Next
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button onClick={handleComplete}>
              <Check className="w-4 h-4 mr-2" />
              {editingGroup ? "Update Group" : "Create Group"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}