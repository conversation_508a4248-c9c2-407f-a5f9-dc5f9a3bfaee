import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { Check, Send, Shield, Building, FileText, Mail, ChevronLeft, ChevronRight, UserPlus, X } from "lucide-react";
import { roles } from "@/data/roles";
import { applications } from "@/data/applications";
import { properties } from "@/data/properties";
import { groups } from "@/data/groups";
import type { AccessRequest } from "@/types";
import { APPLICATION_PAGES } from "@/constants/applications";

interface PermissionWizardProps {
  open: boolean;
  onClose: () => void;
  requests: AccessRequest[];
  onComplete: () => void;
}

const pages = APPLICATION_PAGES;

export default function PermissionWizard({ open, onClose, requests, onComplete }: PermissionWizardProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [assignmentType, setAssignmentType] = useState<"roles" | "groups">("roles");
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);
  const [selectedPages, setSelectedPages] = useState<Record<string, string[]>>({});
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [sendEmail, setSendEmail] = useState(true);
  const [emailMessage, setEmailMessage] = useState("Your access to WillowBridge applications has been approved.");
  const [auth0Email, setAuth0Email] = useState("");
  const [auth0Password, setAuth0Password] = useState("");
  const [approvalStatus, setApprovalStatus] = useState<"pending" | "approved" | "denied">("pending");
  const [rejectionReason, setRejectionReason] = useState("");

  // When groups are selected, we have fewer steps since groups already have permissions
  const totalSteps = assignmentType === "groups" && selectedGroups.length > 0 ? 4 : 6;
  const progress = (currentStep / totalSteps) * 100;

  const handleApplicationToggle = (appId: string, checked: boolean) => {
    if (checked) {
      setSelectedApplications([...selectedApplications, appId]);
      setSelectedPages({ ...selectedPages, [appId]: [] });
    } else {
      setSelectedApplications(selectedApplications.filter((id) => id !== appId));
      const newPages = { ...selectedPages };
      delete newPages[appId];
      setSelectedPages(newPages);
    }
  };

  const handlePageToggle = (appId: string, page: string, checked: boolean) => {
    const appPages = selectedPages[appId] || [];
    if (checked) {
      setSelectedPages({
        ...selectedPages,
        [appId]: [...appPages, page],
      });
    } else {
      setSelectedPages({
        ...selectedPages,
        [appId]: appPages.filter((p) => p !== page),
      });
    }
  };

  const handlePropertyToggle = (propId: string, checked: boolean) => {
    if (checked) {
      setSelectedProperties([...selectedProperties, propId]);
    } else {
      setSelectedProperties(selectedProperties.filter((id) => id !== propId));
    }
  };

  const handleNext = () => {
    // If request is denied in step 1, handle the denial
    if (currentStep === 1 && approvalStatus === "denied") {
      // Send rejection notification and update audit log
      onComplete(); // This will trigger the parent to update UI
      onClose();
      return;
    }

    // Skip steps when groups are selected
    if (assignmentType === "groups" && selectedGroups.length > 0) {
      if (currentStep === 3) {
        // Skip from step 3 (applications/properties) to step 6 (review)
        setCurrentStep(6);
      } else if (currentStep < totalSteps) {
        setCurrentStep(currentStep + 1);
      }
    } else {
      if (currentStep < 6) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    onComplete();
    onClose();
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Permission Assignment Wizard</DialogTitle>
          <div className="flex items-center gap-2 mt-2">
            <Progress value={progress} className="flex-1" />
            <span className="text-sm text-slate-500">
              Step {currentStep} of {totalSteps}
            </span>
          </div>
        </DialogHeader>

        <div className="py-6">
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <UserPlus className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 1: Create Auth0 Account</h3>
                  <p className="text-sm text-slate-600">Create user account and review access request</p>
                </div>
              </div>

              <Card className="p-4 bg-blue-50 border-blue-200">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm mb-1">Access Request Details</h4>
                      <div className="space-y-3">
                        {requests.map((request) => (
                          <div key={request.id} className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Avatar className="w-6 h-6">
                                <AvatarFallback className="text-xs bg-primary/10 text-primary">{getInitials(request.userName)}</AvatarFallback>
                              </Avatar>
                              <span className="font-medium">{request.userName}</span>
                              <span className="text-slate-500">requested</span>
                              <Badge variant="outline" className="text-xs">
                                {request.requestedRole}
                              </Badge>
                            </div>
                            {request.reason && (
                              <div className="ml-8 p-3 bg-white rounded-lg border border-blue-100">
                                <p className="text-xs font-medium text-slate-700 mb-1">Request Message:</p>
                                <p className="text-sm text-slate-600 italic">"{request.reason}"</p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <div className="space-y-4">
                <div>
                  <Label>Auth0 Account Details</Label>
                  <p className="text-xs text-slate-500 mt-1">Create user accounts in Auth0 identity management system</p>
                </div>

                <div className="space-y-3">
                  <div>
                    <Label htmlFor="auth0-email">Email Address</Label>
                    <Input
                      id="auth0-email"
                      type="email"
                      value={auth0Email || requests[0]?.userEmail || ""}
                      onChange={(e) => setAuth0Email(e.target.value)}
                      placeholder="<EMAIL>"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="auth0-password">Temporary Password</Label>
                    <Input
                      id="auth0-password"
                      type="password"
                      value={auth0Password}
                      onChange={(e) => setAuth0Password(e.target.value)}
                      placeholder="Enter temporary password"
                      className="mt-1"
                    />
                    <p className="text-xs text-slate-500 mt-1">User will be required to change password on first login</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <Label>Review and Approve Request</Label>
                <RadioGroup value={approvalStatus} onValueChange={(value: string) => setApprovalStatus(value as "pending" | "approved" | "denied")}>
                  <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-green-50 hover:border-green-200">
                    <RadioGroupItem value="approved" id="approve" />
                    <Label htmlFor="approve" className="flex-1 cursor-pointer">
                      <div className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-green-600" />
                        <span>Approve Request</span>
                      </div>
                      <p className="text-xs text-slate-500 mt-1">Create Auth0 account and proceed with permission assignment</p>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-red-50 hover:border-red-200">
                    <RadioGroupItem value="denied" id="deny" />
                    <Label htmlFor="deny" className="flex-1 cursor-pointer">
                      <div className="flex items-center gap-2">
                        <X className="w-4 h-4 text-red-600" />
                        <span>Deny Request</span>
                      </div>
                      <p className="text-xs text-slate-500 mt-1">Reject the access request and send notification</p>
                    </Label>
                  </div>
                </RadioGroup>

                {approvalStatus === "denied" && (
                  <div className="mt-3">
                    <Label htmlFor="rejection-reason">Rejection Reason</Label>
                    <Textarea
                      id="rejection-reason"
                      value={rejectionReason}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setRejectionReason(e.target.value)}
                      placeholder="Please provide a reason for rejection..."
                      className="mt-1"
                      rows={3}
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Shield className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 2: Assign Roles or Groups</h3>
                  <p className="text-sm text-slate-600">Choose roles or groups for the selected users</p>
                </div>
              </div>

              <div className="space-y-3">
                <Label>Selected Users ({requests.length})</Label>
                <div className="flex flex-wrap gap-2">
                  {requests.map((request) => (
                    <Badge key={request.id} variant="secondary" className="py-1">
                      <Avatar className="w-5 h-5 mr-2">
                        <AvatarFallback className="text-xs bg-primary/10 text-primary">{getInitials(request.userName)}</AvatarFallback>
                      </Avatar>
                      {request.userName}
                    </Badge>
                  ))}
                </div>
              </div>

              <Tabs value={assignmentType} onValueChange={(value) => setAssignmentType(value as "roles" | "groups")}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="roles">Individual Roles</TabsTrigger>
                  <TabsTrigger value="groups">Groups</TabsTrigger>
                </TabsList>

                <TabsContent value="roles" className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>Assign Roles</Label>
                    <span className="text-sm text-slate-500">{selectedRoles.length} selected</span>
                  </div>

                  <div className="max-h-64 overflow-y-auto space-y-2 border rounded-lg p-3">
                    {roles.map((role) => (
                      <div key={role.id} className="flex items-start space-x-2 p-2 hover:bg-slate-50 rounded">
                        <Checkbox
                          id={`role-${role.id}`}
                          checked={selectedRoles.includes(role.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedRoles([...selectedRoles, role.id]);
                            } else {
                              setSelectedRoles(selectedRoles.filter((r) => r !== role.id));
                            }
                          }}
                        />
                        <Label htmlFor={`role-${role.id}`} className="cursor-pointer flex-1">
                          <div className="font-medium text-sm">{role.name}</div>
                          <div className="text-xs text-slate-500">{role.description}</div>
                        </Label>
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="groups" className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>Assign to Groups</Label>
                    <span className="text-sm text-slate-500">{selectedGroups.length} selected</span>
                  </div>

                  <div className="max-h-64 overflow-y-auto space-y-2 border rounded-lg p-3">
                    {groups.map((group) => (
                      <div key={group.id} className="flex items-start space-x-2 p-2 hover:bg-slate-50 rounded">
                        <Checkbox
                          id={`group-${group.id}`}
                          checked={selectedGroups.includes(group.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedGroups([...selectedGroups, group.id]);
                            } else {
                              setSelectedGroups(selectedGroups.filter((g) => g !== group.id));
                            }
                          }}
                        />
                        <Label htmlFor={`group-${group.id}`} className="cursor-pointer flex-1">
                          <div className="font-medium text-sm">{group.name}</div>
                          <div className="text-xs text-slate-500">{group.description}</div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {group.permissions.length} permissions
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {group.memberCount} members
                            </Badge>
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>

              {(selectedRoles.length > 0 || selectedGroups.length > 0) && (
                <Card className="p-4 bg-primary/5 border-primary/20">
                  <div className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-primary mt-0.5" />
                    <div className="space-y-2">
                      {selectedRoles.length > 0 && (
                        <>
                          <p className="font-medium text-sm">Selected Roles:</p>
                          <div className="flex flex-wrap gap-2">
                            {selectedRoles.map((roleId) => {
                              const role = roles.find((r) => r.id === roleId);
                              return role ? (
                                <Badge key={roleId} variant="secondary" className="text-xs">
                                  {role.name}
                                </Badge>
                              ) : null;
                            })}
                          </div>
                        </>
                      )}
                      {selectedGroups.length > 0 && (
                        <>
                          <p className="font-medium text-sm">Selected Groups:</p>
                          <div className="flex flex-wrap gap-2">
                            {selectedGroups.map((groupId) => {
                              const group = groups.find((g) => g.id === groupId);
                              return group ? (
                                <Badge key={groupId} variant="secondary" className="text-xs">
                                  {group.name}
                                </Badge>
                              ) : null;
                            })}
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </Card>
              )}
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Building className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">
                    Step 3: {assignmentType === "groups" && selectedGroups.length > 0 ? "Select Applications or Properties" : "Select Applications"}
                  </h3>
                  <p className="text-sm text-slate-600">
                    {assignmentType === "groups" && selectedGroups.length > 0
                      ? "Groups already have properties assigned. You can optionally select additional applications or properties."
                      : "Choose which applications users can access"}
                  </p>
                </div>
              </div>

              {assignmentType === "groups" && selectedGroups.length > 0 ? (
                <Tabs defaultValue="applications">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="applications">Applications</TabsTrigger>
                    <TabsTrigger value="properties">Properties</TabsTrigger>
                  </TabsList>

                  <TabsContent value="applications" className="space-y-4">
                    <p className="text-sm text-slate-600">Select additional applications to grant access to:</p>
                    {applications.map((app) => (
                      <Card key={app.id} className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3">
                            <Checkbox
                              id={app.id}
                              checked={selectedApplications.includes(app.id)}
                              onCheckedChange={(checked) => handleApplicationToggle(app.id, checked as boolean)}
                            />
                            <Label htmlFor={app.id} className="cursor-pointer">
                              <div className="font-medium">{app.name}</div>
                              <div className="text-sm text-slate-500 mt-1">{app.description}</div>
                            </Label>
                          </div>
                          {selectedApplications.includes(app.id) && <Badge className="bg-primary/10 text-primary">Selected</Badge>}
                        </div>
                      </Card>
                    ))}
                  </TabsContent>

                  <TabsContent value="properties" className="space-y-4">
                    <p className="text-sm text-slate-600">Select additional properties to grant access to:</p>
                    <div className="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                      {properties.map((property) => (
                        <Card key={property.id} className={`p-3 ${selectedProperties.includes(property.id) ? "border-primary bg-primary/5" : ""}`}>
                          <div className="flex items-start gap-3">
                            <Checkbox
                              id={property.id}
                              checked={selectedProperties.includes(property.id)}
                              onCheckedChange={(checked) => handlePropertyToggle(property.id, checked as boolean)}
                            />
                            <Label htmlFor={property.id} className="cursor-pointer flex-1">
                              <div className="font-medium text-sm">{property.name}</div>
                              <div className="text-xs text-slate-500 mt-1">
                                <Badge variant="outline" className="text-xs">
                                  {property.region}
                                </Badge>
                                <span className="ml-2">{property.type}</span>
                              </div>
                            </Label>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              ) : (
                <div className="space-y-4">
                  {applications.map((app) => (
                    <Card key={app.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <Checkbox
                            id={app.id}
                            checked={selectedApplications.includes(app.id)}
                            onCheckedChange={(checked) => handleApplicationToggle(app.id, checked as boolean)}
                          />
                          <Label htmlFor={app.id} className="cursor-pointer">
                            <div className="font-medium">{app.name}</div>
                            <div className="text-sm text-slate-500 mt-1">{app.description}</div>
                          </Label>
                        </div>
                        {selectedApplications.includes(app.id) && <Badge className="bg-primary/10 text-primary">Selected</Badge>}
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          {currentStep === 4 && assignmentType === "roles" && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <FileText className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 4: Page Permissions</h3>
                  <p className="text-sm text-slate-600">Select which pages users can access in each application</p>
                </div>
              </div>

              {selectedApplications.length > 0 ? (
                <Tabs defaultValue={selectedApplications[0]}>
                  <TabsList className="grid w-full grid-cols-3">
                    {selectedApplications.map((appId) => (
                      <TabsTrigger key={appId} value={appId}>
                        {applications.find((a) => a.id === appId)?.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  {selectedApplications.map((appId) => (
                    <TabsContent key={appId} value={appId} className="space-y-4">
                      <div className="grid grid-cols-2 gap-3">
                        {(pages[appId as keyof typeof pages] || []).map((page) => (
                          <div key={page} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${appId}-${page}`}
                              checked={selectedPages[appId]?.includes(page) || false}
                              onCheckedChange={(checked) => handlePageToggle(appId, page, checked as boolean)}
                            />
                            <Label htmlFor={`${appId}-${page}`} className="cursor-pointer text-sm">
                              {page}
                            </Label>
                          </div>
                        ))}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setSelectedPages({
                              ...selectedPages,
                              [appId]: pages[appId as keyof typeof pages] || [],
                            })
                          }
                        >
                          Select All
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setSelectedPages({
                              ...selectedPages,
                              [appId]: [],
                            })
                          }
                        >
                          Clear All
                        </Button>
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              ) : (
                <Card className="p-8 text-center text-slate-500">No applications selected. Please go back and select applications first.</Card>
              )}
            </div>
          )}

          {currentStep === 5 && assignmentType === "roles" && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Building className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 5: Property Access</h3>
                  <p className="text-sm text-slate-600">Select which properties users can access</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedProperties(properties.filter((p) => p.region === "West").map((p) => p.id))}
                  >
                    Select West Region
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedProperties(properties.filter((p) => p.region === "East").map((p) => p.id))}
                  >
                    Select East Region
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setSelectedProperties(properties.map((p) => p.id))}>
                    Select All
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setSelectedProperties([])}>
                    Clear All
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                  {properties.map((property) => (
                    <Card key={property.id} className={`p-3 ${selectedProperties.includes(property.id) ? "border-primary bg-primary/5" : ""}`}>
                      <div className="flex items-start gap-3">
                        <Checkbox
                          id={property.id}
                          checked={selectedProperties.includes(property.id)}
                          onCheckedChange={(checked) => handlePropertyToggle(property.id, checked as boolean)}
                        />
                        <Label htmlFor={property.id} className="cursor-pointer flex-1">
                          <div className="font-medium text-sm">{property.name}</div>
                          <div className="text-xs text-slate-500 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {property.region}
                            </Badge>
                            <span className="ml-2">{property.type}</span>
                          </div>
                        </Label>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          )}

          {currentStep === 6 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Mail className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 6: Review & Send</h3>
                  <p className="text-sm text-slate-600">Review permissions and send notification</p>
                </div>
              </div>

              <Card className="p-4 space-y-4">
                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-2">Summary</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-500">Users:</span>
                      <span className="font-medium">{requests.length} selected</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-500">Roles:</span>
                      <span className="font-medium">{selectedRoles.length} selected</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-500">Groups:</span>
                      <span className="font-medium">{selectedGroups.length} selected</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-500">Applications:</span>
                      <span className="font-medium">{selectedApplications.length} selected</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-500">Properties:</span>
                      <span className="font-medium">{selectedProperties.length} selected</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="send-email" checked={sendEmail} onCheckedChange={(checked) => setSendEmail(checked as boolean)} />
                    <Label htmlFor="send-email">Send email notification to users</Label>
                  </div>
                  {sendEmail && (
                    <textarea
                      className="w-full p-3 text-sm border rounded-lg"
                      rows={3}
                      value={emailMessage}
                      onChange={(e) => setEmailMessage(e.target.value)}
                      placeholder="Custom message..."
                    />
                  )}
                </div>
              </Card>

              <Card className="p-4 bg-green-50 border-green-200">
                <div className="flex items-start gap-3">
                  <Check className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-sm text-green-900">Ready to assign permissions</p>
                    <p className="text-xs text-green-700 mt-1">Click "Complete" to assign these permissions to the selected users.</p>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleBack} disabled={currentStep === 1}>
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          {currentStep < totalSteps ? (
            <Button
              onClick={handleNext}
              disabled={
                (currentStep === 1 &&
                  (approvalStatus === "pending" || !auth0Email || !auth0Password || (approvalStatus === "denied" && !rejectionReason))) ||
                (currentStep === 2 && selectedRoles.length === 0 && selectedGroups.length === 0) ||
                (currentStep === 3 && assignmentType === "roles" && selectedApplications.length === 0)
              }
            >
              {currentStep === 1 && approvalStatus === "denied" ? (
                <>
                  Deny Request
                  <X className="w-4 h-4 ml-2" />
                </>
              ) : (
                <>
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleComplete}>
              <Send className="w-4 h-4 mr-2" />
              Complete Assignment
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
