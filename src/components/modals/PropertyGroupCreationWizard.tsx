import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Building2, ChevronLeft, ChevronRight, Check, Map } from "lucide-react";
import type { PropertyGroup } from "@/types";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllProperties } from "@/store/slices/propertiesSlice";
import { addPropertyGroup, updatePropertyGroup } from "@/store/slices/propertyG<PERSON>sSlice";

interface PropertyGroupCreationWizardProps {
  open: boolean;
  onClose: () => void;
  editingGroup?: PropertyGroup | null;
}

export default function PropertyGroupCreationWizard({ open, onClose, editingGroup }: PropertyGroupCreationWizardProps) {
  const properties = useAppSelector(selectAllProperties);
  const dispatch = useAppDispatch();
  const [currentStep, setCurrentStep] = useState(1);
  const [groupName, setGroupName] = useState("");
  const [groupDescription, setGroupDescription] = useState("");
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  useEffect(() => {
    if (editingGroup) {
      setGroupName(editingGroup.name);
      setGroupDescription(editingGroup.description);
      setSelectedProperties(editingGroup.properties);
    } else {
      resetForm();
    }
  }, [editingGroup]);

  const resetForm = () => {
    setGroupName("");
    setGroupDescription("");
    setSelectedProperties([]);
    setCurrentStep(1);
  };

  const handlePropertyToggle = (propertyId: string) => {
    setSelectedProperties((prev) => (prev.includes(propertyId) ? prev.filter((id) => id !== propertyId) : [...prev, propertyId]));
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1:
        return groupName.trim() !== "" && groupDescription.trim() !== "";
      case 2:
        return selectedProperties.length > 0;
      case 3:
        return true;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    const propertyGroup: PropertyGroup = {
      id: editingGroup?.id || `pg-${Date.now()}`,
      name: groupName,
      description: groupDescription,
      properties: selectedProperties,
      createdAt: editingGroup?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (editingGroup) {
      dispatch(updatePropertyGroup(propertyGroup));
    } else {
      dispatch(addPropertyGroup(propertyGroup));
    }

    onClose();
    resetForm();
  };

  const handleCancel = () => {
    onClose();
    resetForm();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Building2 className="w-5 h-5 text-primary" />
              </div>
              <h3 className="text-lg font-semibold">Basic Information</h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="groupName">Group Name</Label>
              <Input id="groupName" value={groupName} onChange={(e) => setGroupName(e.target.value)} placeholder="e.g., Downtown Properties" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="groupDescription">Description</Label>
              <Textarea
                id="groupDescription"
                value={groupDescription}
                onChange={(e) => setGroupDescription(e.target.value)}
                placeholder="Describe the purpose of this property group..."
                rows={3}
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Map className="w-5 h-5 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold">Select Properties</h3>
                <p className="text-sm text-slate-600">Choose properties to include in this group</p>
              </div>
              <Badge variant="secondary">{selectedProperties.length} selected</Badge>
            </div>

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {properties.map((property) => (
                <Card key={property.id} className="p-3 hover:bg-slate-50 transition-colors">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <Checkbox checked={selectedProperties.includes(property.id)} onCheckedChange={() => handlePropertyToggle(property.id)} />
                    <div className="flex-1">
                      <p className="font-medium">{property.name}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {property.type}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {property.region}
                        </Badge>
                      </div>
                    </div>
                  </label>
                </Card>
              ))}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Check className="w-5 h-5 text-primary" />
              </div>
              <h3 className="text-lg font-semibold">Review & Create</h3>
            </div>

            <Card className="p-4 space-y-3">
              <div>
                <Label className="text-slate-600">Group Name</Label>
                <p className="font-medium">{groupName}</p>
              </div>

              <div>
                <Label className="text-slate-600">Description</Label>
                <p className="text-sm">{groupDescription}</p>
              </div>

              <div>
                <Label className="text-slate-600">Selected Properties ({selectedProperties.length})</Label>
                <div className="mt-2 space-y-1">
                  {selectedProperties.map((propertyId) => {
                    const property = properties.find((p) => p.id === propertyId);
                    return property ? (
                      <div key={property.id} className="flex items-center justify-between p-2 bg-slate-50 rounded">
                        <span className="text-sm font-medium">{property.name}</span>
                        <div className="flex gap-1">
                          <Badge variant="outline" className="text-xs">
                            {property.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {property.region}
                          </Badge>
                        </div>
                      </div>
                    ) : null;
                  })}
                </div>
              </div>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleCancel}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{editingGroup ? "Edit Property Group" : "Create Property Group"}</DialogTitle>
          <DialogDescription>
            {editingGroup ? "Update the property group information" : "Create a new group to organize properties"}
          </DialogDescription>
          <Progress value={progress} className="mt-4" />
        </DialogHeader>

        <div className="py-4">{renderStepContent()}</div>

        <DialogFooter>
          <div className="flex items-center justify-between w-full">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>

            <div className="flex items-center space-x-2">
              {currentStep > 1 && (
                <Button variant="outline" onClick={handleBack}>
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
              )}

              {currentStep < totalSteps ? (
                <Button onClick={handleNext} disabled={!canProceedToNextStep()}>
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button onClick={handleComplete} disabled={!canProceedToNextStep()}>
                  <Check className="w-4 h-4 mr-2" />
                  {editingGroup ? "Update Group" : "Create Group"}
                </Button>
              )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
