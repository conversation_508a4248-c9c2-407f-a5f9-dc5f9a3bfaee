import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card } from '@/components/ui/card';
import { 
  Shield, 
  ChevronLeft, 
  ChevronRight, 
  Check,
  Plus,
  Minus,
  AppWindow
} from 'lucide-react';
import { applications } from '@/data/applications';
import { applicationPages, getDefaultApplicationPermissions } from '@/data/permissions';
import type { Role, ApplicationPermission } from '@/types';

interface RoleCreationWizardProps {
  open: boolean;
  onClose: () => void;
  editingRole?: Role | null;
  onComplete: (role: Partial<Role>) => void;
}

export default function RoleCreationWizard({ 
  open, 
  onClose, 
  editingRole,
  onComplete 
}: RoleCreationWizardProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [roleName, setRoleName] = useState('');
  const [roleDescription, setRoleDescription] = useState('');
  const [selectedApplication, setSelectedApplication] = useState<string>('');
  const [applicationPermission, setApplicationPermission] = useState<ApplicationPermission | null>(null);

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  useEffect(() => {
    if (editingRole) {
      setRoleName(editingRole.name);
      setRoleDescription(editingRole.description);
      if (editingRole.applicationPermissions && editingRole.applicationPermissions.length > 0) {
        const firstAppPermission = editingRole.applicationPermissions[0];
        setApplicationPermission(firstAppPermission);
        setSelectedApplication(firstAppPermission.applicationId);
      }
    } else {
      resetForm();
    }
  }, [editingRole]);

  const resetForm = () => {
    setRoleName('');
    setRoleDescription('');
    setSelectedApplication('');
    setApplicationPermission(null);
    setCurrentStep(1);
  };

  const handleApplicationSelect = (appId: string) => {
    const app = applications.find(a => a.id === appId);
    if (!app) return;

    setSelectedApplication(appId);
    const defaultPermissions = getDefaultApplicationPermissions(appId, app.name);
    setApplicationPermission(defaultPermissions);
  };

  const handlePagePermissionToggle = (
    pageId: string, 
    operation: 'create' | 'read' | 'update' | 'delete',
    checked: boolean
  ) => {
    if (!applicationPermission) return;

    setApplicationPermission({
      ...applicationPermission,
      pages: applicationPermission.pages.map(page => {
        if (page.pageId === pageId) {
          return { ...page, [operation]: checked };
        }
        return page;
      })
    });
  };

  const handleSelectAllPermissions = (select: boolean) => {
    if (!applicationPermission) return;

    setApplicationPermission({
      ...applicationPermission,
      pages: applicationPermission.pages.map(page => ({
        ...page,
        create: select,
        read: select,
        update: select,
        delete: select
      }))
    });
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    const role: Partial<Role> = {
      id: editingRole?.id || '',
      name: roleName,
      description: roleDescription,
      permissions: [],
      applicationPermissions: applicationPermission ? [applicationPermission] : []
    };
    
    onComplete(role);
    resetForm();
  };

  const getTotalPermissionCount = () => {
    if (!applicationPermission) return 0;
    
    return applicationPermission.pages.reduce((total, page) => {
      let count = 0;
      if (page.create) count++;
      if (page.read) count++;
      if (page.update) count++;
      if (page.delete) count++;
      return total + count;
    }, 0);
  };

  const getSelectedAppName = () => {
    const app = applications.find(a => a.id === selectedApplication);
    return app?.name || '';
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {editingRole ? 'Edit Role' : 'Create New Role'}
          </DialogTitle>
          <div className="flex items-center gap-2 mt-2">
            <Progress value={progress} className="flex-1" />
            <span className="text-sm text-slate-500">
              Step {currentStep} of {totalSteps}
            </span>
          </div>
        </DialogHeader>

        <div className="py-6">
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Shield className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 1: Role Information</h3>
                  <p className="text-sm text-slate-600">Define the role name and description</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="role-name">Role Name *</Label>
                  <Input
                    id="role-name"
                    value={roleName}
                    onChange={(e) => setRoleName(e.target.value)}
                    placeholder="e.g., Prism Administrator"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="role-description">Role Description *</Label>
                  <Textarea
                    id="role-description"
                    value={roleDescription}
                    onChange={(e) => setRoleDescription(e.target.value)}
                    placeholder="Describe the purpose and responsibilities of this role..."
                    className="mt-1"
                    rows={4}
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <AppWindow className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 2: {selectedApplication ? getSelectedAppName() : 'Select Application'}</h3>
                  <p className="text-sm text-slate-600">
                    {selectedApplication 
                      ? `Configure permissions for ${getSelectedAppName()}`
                      : 'Choose which application this role can access'
                    }
                  </p>
                </div>
              </div>

              {!selectedApplication ? (
                <RadioGroup value={selectedApplication} onValueChange={handleApplicationSelect}>
                  <div className="space-y-4">
                    {applications.map((app) => (
                      <Card key={app.id} className="p-4">
                        <div className="flex items-start gap-3">
                          <RadioGroupItem value={app.id} id={app.id} />
                          <Label htmlFor={app.id} className="cursor-pointer flex-1">
                            <div className="font-medium">{app.name}</div>
                            <div className="text-sm text-slate-500 mt-1">{app.description}</div>
                            <div className="text-xs text-slate-400 mt-2">
                              {applicationPages[app.id]?.length || 0} pages available
                            </div>
                          </Label>
                        </div>
                      </Card>
                    ))}
                  </div>
                </RadioGroup>
              ) : (
                <div className="space-y-4">
                  <Card className="p-4 bg-primary/5 border-primary/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{getSelectedAppName()}</h4>
                        <p className="text-sm text-slate-600 mt-1">
                          {applications.find(a => a.id === selectedApplication)?.description}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedApplication('');
                          setApplicationPermission(null);
                        }}
                      >
                        Change Application
                      </Button>
                    </div>
                  </Card>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">Page Permissions</h4>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSelectAllPermissions(true)}
                        >
                          <Plus className="w-4 h-4 mr-1" />
                          Select All
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSelectAllPermissions(false)}
                        >
                          <Minus className="w-4 h-4 mr-1" />
                          Clear All
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="grid grid-cols-6 gap-2 text-xs font-medium text-slate-600 pb-2 border-b">
                        <div className="col-span-2">Page Name</div>
                        <div className="text-center">Create</div>
                        <div className="text-center">Read</div>
                        <div className="text-center">Update</div>
                        <div className="text-center">Delete</div>
                      </div>

                      {applicationPermission?.pages.map((page) => (
                        <div key={page.pageId} className="grid grid-cols-6 gap-2 items-center py-2 hover:bg-slate-50 rounded px-2">
                          <div className="col-span-2 font-medium text-sm">{page.pageName}</div>
                          <div className="text-center">
                            <Checkbox
                              checked={page.create}
                              onCheckedChange={(checked) => 
                                handlePagePermissionToggle(page.pageId, 'create', checked as boolean)
                              }
                            />
                          </div>
                          <div className="text-center">
                            <Checkbox
                              checked={page.read}
                              onCheckedChange={(checked) => 
                                handlePagePermissionToggle(page.pageId, 'read', checked as boolean)
                              }
                            />
                          </div>
                          <div className="text-center">
                            <Checkbox
                              checked={page.update}
                              onCheckedChange={(checked) => 
                                handlePagePermissionToggle(page.pageId, 'update', checked as boolean)
                              }
                            />
                          </div>
                          <div className="text-center">
                            <Checkbox
                              checked={page.delete}
                              onCheckedChange={(checked) => 
                                handlePagePermissionToggle(page.pageId, 'delete', checked as boolean)
                              }
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Check className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">Step 3: Review & Create</h3>
                  <p className="text-sm text-slate-600">Review the role configuration before creating</p>
                </div>
              </div>

              <Card className="p-4 space-y-4">
                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-2">Role Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-500">Name:</span>
                      <span className="font-medium">{roleName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-500">Description:</span>
                      <span className="font-medium text-right max-w-xs">{roleDescription}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-2">Application Access</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-500">Application:</span>
                      <span className="font-medium">{getSelectedAppName()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-500">Total Permissions:</span>
                      <span className="font-medium">{getTotalPermissionCount()}</span>
                    </div>
                  </div>
                </div>

                {applicationPermission && (
                  <div>
                    <h4 className="font-medium text-sm text-slate-700 mb-2">Permission Summary</h4>
                    <div className="space-y-2">
                      {applicationPermission.pages.filter(page => 
                        page.create || page.read || page.update || page.delete
                      ).map(page => {
                        const permissions = [];
                        if (page.create) permissions.push('Create');
                        if (page.read) permissions.push('Read');
                        if (page.update) permissions.push('Update');
                        if (page.delete) permissions.push('Delete');
                        
                        return (
                          <div key={page.pageId} className="flex items-center justify-between p-2 bg-slate-50 rounded">
                            <span className="text-sm font-medium">{page.pageName}</span>
                            <div className="flex gap-1">
                              {permissions.map(perm => (
                                <Badge key={perm} variant="secondary" className="text-xs">
                                  {perm}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </Card>

              <Card className="p-4 bg-green-50 border-green-200">
                <div className="flex items-start gap-3">
                  <Check className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-sm text-green-900">
                      {editingRole ? 'Ready to update role' : 'Ready to create role'}
                    </p>
                    <p className="text-xs text-green-700 mt-1">
                      Click "{editingRole ? 'Update' : 'Create'}" to {editingRole ? 'save changes' : 'create this role'}.
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button 
            variant="outline" 
            onClick={handleBack} 
            disabled={currentStep === 1}
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          
          {currentStep < totalSteps ? (
            <Button
              onClick={handleNext}
              disabled={
                (currentStep === 1 && (!roleName || !roleDescription)) ||
                (currentStep === 2 && !selectedApplication)
              }
            >
              Next
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button onClick={handleComplete}>
              <Check className="w-4 h-4 mr-2" />
              {editingRole ? 'Update Role' : 'Create Role'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}