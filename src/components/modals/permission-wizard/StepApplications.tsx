import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Building } from "lucide-react";
import { applications } from "@/data/applications";
import { properties } from "@/data/properties";

interface StepApplicationsProps {
  assignmentType: "roles" | "groups";
  selectedGroups: string[];
  selectedApplications: string[];
  selectedProperties: string[];
  onApplicationToggle: (appId: string, checked: boolean) => void;
  onPropertyToggle: (propId: string, checked: boolean) => void;
}

export default function StepApplications({
  assignmentType,
  selectedGroups,
  selectedApplications,
  selectedProperties,
  onApplicationToggle,
  onPropertyToggle,
}: StepApplicationsProps) {
  const showPropertyTab = assignmentType === "groups" && selectedGroups.length > 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Building className="w-8 h-8 text-primary" />
        <div>
          <h3 className="text-lg font-semibold">
            Step 3: {showPropertyTab ? "Select Applications or Properties" : "Select Applications"}
          </h3>
          <p className="text-sm text-slate-600">
            {showPropertyTab
              ? "Groups already have properties assigned. You can optionally select additional applications or properties."
              : "Choose which applications users can access"}
          </p>
        </div>
      </div>

      {showPropertyTab ? (
        <Tabs defaultValue="applications">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="applications">Applications</TabsTrigger>
            <TabsTrigger value="properties">Properties</TabsTrigger>
          </TabsList>

          <TabsContent value="applications" className="space-y-4">
            <p className="text-sm text-slate-600">Select additional applications to grant access to:</p>
            {applications.map((app) => (
              <Card key={app.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <Checkbox
                      id={app.id}
                      checked={selectedApplications.includes(app.id)}
                      onCheckedChange={(checked) => onApplicationToggle(app.id, checked as boolean)}
                    />
                    <Label htmlFor={app.id} className="cursor-pointer">
                      <div className="font-medium">{app.name}</div>
                      <div className="text-sm text-slate-500 mt-1">{app.description}</div>
                    </Label>
                  </div>
                  {selectedApplications.includes(app.id) && (
                    <Badge className="bg-primary/10 text-primary">Selected</Badge>
                  )}
                </div>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="properties" className="space-y-4">
            <p className="text-sm text-slate-600">Select additional properties to grant access to:</p>
            <div className="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
              {properties.map((property) => (
                <Card
                  key={property.id}
                  className={`p-3 ${
                    selectedProperties.includes(property.id) ? "border-primary bg-primary/5" : ""
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <Checkbox
                      id={property.id}
                      checked={selectedProperties.includes(property.id)}
                      onCheckedChange={(checked) => onPropertyToggle(property.id, checked as boolean)}
                    />
                    <Label htmlFor={property.id} className="cursor-pointer flex-1">
                      <div className="font-medium text-sm">{property.name}</div>
                      <div className="text-xs text-slate-500 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {property.region}
                        </Badge>
                        <span className="ml-2">{property.type}</span>
                      </div>
                    </Label>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      ) : (
        <div className="space-y-4">
          {applications.map((app) => (
            <Card key={app.id} className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <Checkbox
                    id={app.id}
                    checked={selectedApplications.includes(app.id)}
                    onCheckedChange={(checked) => onApplicationToggle(app.id, checked as boolean)}
                  />
                  <Label htmlFor={app.id} className="cursor-pointer">
                    <div className="font-medium">{app.name}</div>
                    <div className="text-sm text-slate-500 mt-1">{app.description}</div>
                  </Label>
                </div>
                {selectedApplications.includes(app.id) && (
                  <Badge className="bg-primary/10 text-primary">Selected</Badge>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}