import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { UserPlus, Check, X } from "lucide-react";
import type { AccessRequest } from "@/types";

interface StepAuth0AccountProps {
  requests: AccessRequest[];
  auth0Email: string;
  auth0Password: string;
  approvalStatus: "pending" | "approved" | "denied";
  rejectionReason: string;
  onAuth0EmailChange: (value: string) => void;
  onAuth0PasswordChange: (value: string) => void;
  onApprovalStatusChange: (value: "pending" | "approved" | "denied") => void;
  onRejectionReasonChange: (value: string) => void;
}

const getInitials = (name: string) => {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

export default function StepAuth0Account({
  requests,
  auth0Email,
  auth0Password,
  approvalStatus,
  rejectionReason,
  onAuth0EmailChange,
  onAuth0PasswordChange,
  onApprovalStatusChange,
  onRejectionReasonChange,
}: StepAuth0AccountProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <UserPlus className="w-8 h-8 text-primary" />
        <div>
          <h3 className="text-lg font-semibold">Step 1: Create Auth0 Account</h3>
          <p className="text-sm text-slate-600">Create user account and review access request</p>
        </div>
      </div>

      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <div className="flex-1">
              <h4 className="font-medium text-sm mb-1">Access Request Details</h4>
              <div className="space-y-3">
                {requests.map((request) => (
                  <div key={request.id} className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Avatar className="w-6 h-6">
                        <AvatarFallback className="text-xs bg-primary/10 text-primary">
                          {getInitials(request.userName)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium">{request.userName}</span>
                      <span className="text-slate-500">requested</span>
                      <Badge variant="outline" className="text-xs">
                        {request.requestedRole}
                      </Badge>
                    </div>
                    {request.reason && (
                      <div className="ml-8 p-3 bg-white rounded-lg border border-blue-100">
                        <p className="text-xs font-medium text-slate-700 mb-1">Request Message:</p>
                        <p className="text-sm text-slate-600 italic">"{request.reason}"</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Card>

      <div className="space-y-4">
        <div>
          <Label>Auth0 Account Details</Label>
          <p className="text-xs text-slate-500 mt-1">Create user accounts in Auth0 identity management system</p>
        </div>

        <div className="space-y-3">
          <div>
            <Label htmlFor="auth0-email">Email Address</Label>
            <Input
              id="auth0-email"
              type="email"
              value={auth0Email || requests[0]?.userEmail || ""}
              onChange={(e) => onAuth0EmailChange(e.target.value)}
              placeholder="<EMAIL>"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="auth0-password">Temporary Password</Label>
            <Input
              id="auth0-password"
              type="password"
              value={auth0Password}
              onChange={(e) => onAuth0PasswordChange(e.target.value)}
              placeholder="Enter temporary password"
              className="mt-1"
            />
            <p className="text-xs text-slate-500 mt-1">User will be required to change password on first login</p>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <Label>Review and Approve Request</Label>
        <RadioGroup 
          value={approvalStatus} 
          onValueChange={(value: string) => onApprovalStatusChange(value as "pending" | "approved" | "denied")}
        >
          <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-green-50 hover:border-green-200">
            <RadioGroupItem value="approved" id="approve" />
            <Label htmlFor="approve" className="flex-1 cursor-pointer">
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-600" />
                <span>Approve Request</span>
              </div>
              <p className="text-xs text-slate-500 mt-1">Create Auth0 account and proceed with permission assignment</p>
            </Label>
          </div>

          <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-red-50 hover:border-red-200">
            <RadioGroupItem value="denied" id="deny" />
            <Label htmlFor="deny" className="flex-1 cursor-pointer">
              <div className="flex items-center gap-2">
                <X className="w-4 h-4 text-red-600" />
                <span>Deny Request</span>
              </div>
              <p className="text-xs text-slate-500 mt-1">Reject the access request and send notification</p>
            </Label>
          </div>
        </RadioGroup>

        {approvalStatus === "denied" && (
          <div className="mt-3">
            <Label htmlFor="rejection-reason">Rejection Reason</Label>
            <Textarea
              id="rejection-reason"
              value={rejectionReason}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => onRejectionReasonChange(e.target.value)}
              placeholder="Please provide a reason for rejection..."
              className="mt-1"
              rows={3}
            />
          </div>
        )}
      </div>
    </div>
  );
}