import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText } from "lucide-react";
import { applications } from "@/data/applications";

interface StepPagePermissionsProps {
  selectedApplications: string[];
  selectedPages: Record<string, string[]>;
  pages: Record<string, string[]>;
  onPageToggle: (appId: string, page: string, checked: boolean) => void;
  onSelectAll: (appId: string) => void;
  onClearAll: (appId: string) => void;
}

export default function StepPagePermissions({
  selectedApplications,
  selectedPages,
  pages,
  onPageToggle,
  onSelectAll,
  onClearAll,
}: StepPagePermissionsProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <FileText className="w-8 h-8 text-primary" />
        <div>
          <h3 className="text-lg font-semibold">Step 4: Page Permissions</h3>
          <p className="text-sm text-slate-600">Select which pages users can access in each application</p>
        </div>
      </div>

      {selectedApplications.length > 0 ? (
        <Tabs defaultValue={selectedApplications[0]}>
          <TabsList className="grid w-full grid-cols-3">
            {selectedApplications.map((appId) => (
              <TabsTrigger key={appId} value={appId}>
                {applications.find((a) => a.id === appId)?.name}
              </TabsTrigger>
            ))}
          </TabsList>
          {selectedApplications.map((appId) => (
            <TabsContent key={appId} value={appId} className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                {(pages[appId as keyof typeof pages] || []).map((page) => (
                  <div key={page} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${appId}-${page}`}
                      checked={selectedPages[appId]?.includes(page) || false}
                      onCheckedChange={(checked) => onPageToggle(appId, page, checked as boolean)}
                    />
                    <Label htmlFor={`${appId}-${page}`} className="cursor-pointer text-sm">
                      {page}
                    </Label>
                  </div>
                ))}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => onSelectAll(appId)}>
                  Select All
                </Button>
                <Button variant="outline" size="sm" onClick={() => onClearAll(appId)}>
                  Clear All
                </Button>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        <Card className="p-8 text-center text-slate-500">
          No applications selected. Please go back and select applications first.
        </Card>
      )}
    </div>
  );
}