import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Building } from "lucide-react";
import { properties } from "@/data/properties";

interface StepPropertyAccessProps {
  selectedProperties: string[];
  onPropertyToggle: (propId: string, checked: boolean) => void;
  onSelectRegion: (region: "West" | "East") => void;
  onSelectAll: () => void;
  onClearAll: () => void;
}

export default function StepPropertyAccess({
  selectedProperties,
  onPropertyToggle,
  onSelectRegion,
  onSelectAll,
  onClearAll,
}: StepPropertyAccessProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Building className="w-8 h-8 text-primary" />
        <div>
          <h3 className="text-lg font-semibold">Step 5: Property Access</h3>
          <p className="text-sm text-slate-600">Select which properties users can access</p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Button variant="outline" size="sm" onClick={() => onSelectRegion("West")}>
            Select West Region
          </Button>
          <Button variant="outline" size="sm" onClick={() => onSelectRegion("East")}>
            Select East Region
          </Button>
          <Button variant="outline" size="sm" onClick={onSelectAll}>
            Select All
          </Button>
          <Button variant="outline" size="sm" onClick={onClearAll}>
            Clear All
          </Button>
        </div>

        <div className="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
          {properties.map((property) => (
            <Card
              key={property.id}
              className={`p-3 ${
                selectedProperties.includes(property.id) ? "border-primary bg-primary/5" : ""
              }`}
            >
              <div className="flex items-start gap-3">
                <Checkbox
                  id={property.id}
                  checked={selectedProperties.includes(property.id)}
                  onCheckedChange={(checked) => onPropertyToggle(property.id, checked as boolean)}
                />
                <Label htmlFor={property.id} className="cursor-pointer flex-1">
                  <div className="font-medium text-sm">{property.name}</div>
                  <div className="text-xs text-slate-500 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {property.region}
                    </Badge>
                    <span className="ml-2">{property.type}</span>
                  </div>
                </Label>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}