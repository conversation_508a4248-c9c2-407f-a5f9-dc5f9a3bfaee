import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Mail, Check } from "lucide-react";
import type { AccessRequest } from "@/types";

interface StepReviewSendProps {
  requests: AccessRequest[];
  selectedRoles: string[];
  selectedGroups: string[];
  selectedApplications: string[];
  selectedProperties: string[];
  sendEmail: boolean;
  emailMessage: string;
  onSendEmailChange: (checked: boolean) => void;
  onEmailMessageChange: (message: string) => void;
}

export default function StepReviewSend({
  requests,
  selectedRoles,
  selectedGroups,
  selectedApplications,
  selectedProperties,
  sendEmail,
  emailMessage,
  onSendEmailChange,
  onEmailMessageChange,
}: StepReviewSendProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Mail className="w-8 h-8 text-primary" />
        <div>
          <h3 className="text-lg font-semibold">Step 6: Review & Send</h3>
          <p className="text-sm text-slate-600">Review permissions and send notification</p>
        </div>
      </div>

      <Card className="p-4 space-y-4">
        <div>
          <h4 className="font-medium text-sm text-slate-700 mb-2">Summary</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-slate-500">Users:</span>
              <span className="font-medium">{requests.length} selected</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-500">Roles:</span>
              <span className="font-medium">{selectedRoles.length} selected</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-500">Groups:</span>
              <span className="font-medium">{selectedGroups.length} selected</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-500">Applications:</span>
              <span className="font-medium">{selectedApplications.length} selected</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-500">Properties:</span>
              <span className="font-medium">{selectedProperties.length} selected</span>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="send-email"
              checked={sendEmail}
              onCheckedChange={(checked) => onSendEmailChange(checked as boolean)}
            />
            <Label htmlFor="send-email">Send email notification to users</Label>
          </div>
          {sendEmail && (
            <textarea
              className="w-full p-3 text-sm border rounded-lg"
              rows={3}
              value={emailMessage}
              onChange={(e) => onEmailMessageChange(e.target.value)}
              placeholder="Custom message..."
            />
          )}
        </div>
      </Card>

      <Card className="p-4 bg-green-50 border-green-200">
        <div className="flex items-start gap-3">
          <Check className="w-5 h-5 text-green-600 mt-0.5" />
          <div>
            <p className="font-medium text-sm text-green-900">Ready to assign permissions</p>
            <p className="text-xs text-green-700 mt-1">
              Click "Complete" to assign these permissions to the selected users.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
}