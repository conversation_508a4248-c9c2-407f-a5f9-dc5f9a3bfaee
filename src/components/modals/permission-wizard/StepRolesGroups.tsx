import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Shield, Check } from "lucide-react";
import { roles } from "@/data/roles";
import { groups } from "@/data/groups";
import type { AccessRequest } from "@/types";

interface StepRolesGroupsProps {
  requests: AccessRequest[];
  selectedRoles: string[];
  selectedGroups: string[];
  assignmentType: "roles" | "groups";
  onRoleToggle: (roleId: string, checked: boolean) => void;
  onGroupToggle: (groupId: string, checked: boolean) => void;
  onAssignmentTypeChange: (type: "roles" | "groups") => void;
}

const getInitials = (name: string) => {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

export default function StepRolesGroups({
  requests,
  selectedRoles,
  selectedGroups,
  assignmentType,
  onRoleToggle,
  onGroupToggle,
  onAssignmentTypeChange,
}: StepRolesGroupsProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Shield className="w-8 h-8 text-primary" />
        <div>
          <h3 className="text-lg font-semibold">Step 2: Assign Roles or Groups</h3>
          <p className="text-sm text-slate-600">Choose roles or groups for the selected users</p>
        </div>
      </div>

      <div className="space-y-3">
        <Label>Selected Users ({requests.length})</Label>
        <div className="flex flex-wrap gap-2">
          {requests.map((request) => (
            <Badge key={request.id} variant="secondary" className="py-1">
              <Avatar className="w-5 h-5 mr-2">
                <AvatarFallback className="text-xs bg-primary/10 text-primary">
                  {getInitials(request.userName)}
                </AvatarFallback>
              </Avatar>
              {request.userName}
            </Badge>
          ))}
        </div>
      </div>

      <Tabs value={assignmentType} onValueChange={(value) => onAssignmentTypeChange(value as "roles" | "groups")}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="roles">Individual Roles</TabsTrigger>
          <TabsTrigger value="groups">Groups</TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-3">
          <div className="flex items-center justify-between">
            <Label>Assign Roles</Label>
            <span className="text-sm text-slate-500">{selectedRoles.length} selected</span>
          </div>

          <div className="max-h-64 overflow-y-auto space-y-2 border rounded-lg p-3">
            {roles.map((role) => (
              <div key={role.id} className="flex items-start space-x-2 p-2 hover:bg-slate-50 rounded">
                <Checkbox
                  id={`role-${role.id}`}
                  checked={selectedRoles.includes(role.id)}
                  onCheckedChange={(checked) => onRoleToggle(role.id, checked as boolean)}
                />
                <Label htmlFor={`role-${role.id}`} className="cursor-pointer flex-1">
                  <div className="font-medium text-sm">{role.name}</div>
                  <div className="text-xs text-slate-500">{role.description}</div>
                </Label>
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="groups" className="space-y-3">
          <div className="flex items-center justify-between">
            <Label>Assign to Groups</Label>
            <span className="text-sm text-slate-500">{selectedGroups.length} selected</span>
          </div>

          <div className="max-h-64 overflow-y-auto space-y-2 border rounded-lg p-3">
            {groups.map((group) => (
              <div key={group.id} className="flex items-start space-x-2 p-2 hover:bg-slate-50 rounded">
                <Checkbox
                  id={`group-${group.id}`}
                  checked={selectedGroups.includes(group.id)}
                  onCheckedChange={(checked) => onGroupToggle(group.id, checked as boolean)}
                />
                <Label htmlFor={`group-${group.id}`} className="cursor-pointer flex-1">
                  <div className="font-medium text-sm">{group.name}</div>
                  <div className="text-xs text-slate-500">{group.description}</div>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {group.permissions.length} permissions
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {group.memberCount} members
                    </Badge>
                  </div>
                </Label>
              </div>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {(selectedRoles.length > 0 || selectedGroups.length > 0) && (
        <Card className="p-4 bg-primary/5 border-primary/20">
          <div className="flex items-start gap-3">
            <Check className="w-5 h-5 text-primary mt-0.5" />
            <div className="space-y-2">
              {selectedRoles.length > 0 && (
                <>
                  <p className="font-medium text-sm">Selected Roles:</p>
                  <div className="flex flex-wrap gap-2">
                    {selectedRoles.map((roleId) => {
                      const role = roles.find((r) => r.id === roleId);
                      return role ? (
                        <Badge key={roleId} variant="secondary" className="text-xs">
                          {role.name}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </>
              )}
              {selectedGroups.length > 0 && (
                <>
                  <p className="font-medium text-sm">Selected Groups:</p>
                  <div className="flex flex-wrap gap-2">
                    {selectedGroups.map((groupId) => {
                      const group = groups.find((g) => g.id === groupId);
                      return group ? (
                        <Badge key={groupId} variant="secondary" className="text-xs">
                          {group.name}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </>
              )}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}