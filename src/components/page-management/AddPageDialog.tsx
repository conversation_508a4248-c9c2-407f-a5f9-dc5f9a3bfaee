import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { addPage, selectPagesByApplicationId } from "@/store/slices/applicationPagesSlice";
import { toast } from "sonner";

interface AddPageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  applicationId: string;
  applicationName: string;
}

export default function AddPageDialog({
  open,
  onOpenChange,
  applicationId,
  applicationName,
}: AddPageDialogProps) {
  const dispatch = useAppDispatch();
  const existingPages = useAppSelector(selectPagesByApplicationId(applicationId));
  const [pageName, setPageName] = useState("");
  const [pageId, setPageId] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handlePageNameChange = (value: string) => {
    setPageName(value);
    setError(null);

    const generatedId = value
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '');
    setPageId(generatedId);
  };

  const handleSubmit = () => {
    const trimmedName = pageName.trim();
    const trimmedId = pageId.trim();

    if (!trimmedName) {
      setError("Page name is required");
      return;
    }

    if (!trimmedId) {
      setError("Page ID is required");
      return;
    }

    if (!/^[a-z0-9_]+$/.test(trimmedId)) {
      setError("Page ID can only contain lowercase letters, numbers, and underscores");
      return;
    }

    const pageExists = existingPages.some(
      page => page.name.toLowerCase() === trimmedName.toLowerCase() ||
              page.id.toLowerCase() === trimmedId.toLowerCase()
    );

    if (pageExists) {
      setError(`A page with this name or ID already exists`);
      return;
    }

    dispatch(
      addPage({
        applicationId,
        page: {
          id: trimmedId,
          name: trimmedName,
        },
      })
    );

    toast.success(`Page "${trimmedName}" added successfully`);
    handleClose();
  };

  const handleClose = () => {
    setPageName("");
    setPageId("");
    setError(null);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Plus className="w-5 h-5 mr-2 text-primary" />
            Add New Page
          </DialogTitle>
          <DialogDescription>
            Add a new page to {applicationName} for permission configuration.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="page-name">Page Name</Label>
            <Input
              id="page-name"
              placeholder="e.g., Dashboard, Reports, Settings"
              value={pageName}
              onChange={(e) => handlePageNameChange(e.target.value)}
              className={error ? "border-red-500" : ""}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="page-id">Page ID</Label>
            <Input
              id="page-id"
              placeholder="e.g., dashboard, reports, settings"
              value={pageId}
              onChange={(e) => setPageId(e.target.value)}
              className={error ? "border-red-500" : ""}
            />
            <p className="text-xs text-slate-500">
              Auto-generated from page name. Use lowercase letters, numbers, and underscores only.
            </p>
          </div>

          {error && (
            <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Add Page</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}