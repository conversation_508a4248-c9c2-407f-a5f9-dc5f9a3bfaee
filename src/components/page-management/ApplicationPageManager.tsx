import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AppWindow, Search, Layers, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAppSelector } from "@/store/hooks";
import { selectAllApplications } from "@/store/slices/applicationsSlice";
import { selectAllApplicationPages } from "@/store/slices/applicationPagesSlice";
import PageList from "./PageList";
import AddPageDialog from "./AddPageDialog";

export default function ApplicationPageManager() {
  const applications = useAppSelector(selectAllApplications);
  const allApplicationPages = useAppSelector(selectAllApplicationPages);
  const [selectedApp, setSelectedApp] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddPageOpen, setIsAddPageOpen] = useState(false);

  const filteredApplications = applications.filter(
    (app) =>
      app.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedApplication = applications.find((app) => app.id === selectedApp);

  return (
    <div className="h-full flex">
      {/* Application List Sidebar */}
      <div className="w-80 border-r bg-slate-50 flex flex-col">
        <div className="p-4 border-b bg-white">
          <h3 className="font-semibold text-lg flex items-center">
            <AppWindow className="w-5 h-5 mr-2 text-primary" />
            Applications
          </h3>
          <p className="text-sm text-slate-600 mt-1">
            Select an application to manage its pages
          </p>
        </div>

        <div className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search applications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 h-9"
            />
          </div>
        </div>

        <ScrollArea className="flex-1 px-4">
          <div className="space-y-2 pb-4">
            {filteredApplications.map((app) => {
              const pageCount = allApplicationPages[app.id]?.length || 0;
              return (
                <Card
                  key={app.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    selectedApp === app.id && "ring-2 ring-primary border-primary"
                  )}
                  onClick={() => setSelectedApp(app.id)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-base font-medium">
                          {app.name}
                        </CardTitle>
                        <p className="text-xs text-slate-600 mt-1">
                          {app.description}
                        </p>
                      </div>
                      <Badge variant="secondary" className="ml-2">
                        {pageCount} pages
                      </Badge>
                    </div>
                  </CardHeader>
                </Card>
              );
            })}
          </div>
        </ScrollArea>
      </div>

      {/* Page Management Area */}
      <div className="flex-1 flex flex-col">
        {selectedApp ? (
          <>
            <div className="p-6 border-b bg-white">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold flex items-center">
                    <Layers className="w-5 h-5 mr-2 text-primary" />
                    {selectedApplication?.name} Pages
                  </h2>
                  <p className="text-sm text-slate-600 mt-1">
                    Manage pages and their permissions for this application
                  </p>
                </div>
                <Button
                  onClick={() => setIsAddPageOpen(true)}
                  className="flex items-center"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Page
                </Button>
              </div>
            </div>

            <div className="flex-1 p-6 overflow-auto">
              <PageList applicationId={selectedApp} />
            </div>

            <AddPageDialog
              open={isAddPageOpen}
              onOpenChange={setIsAddPageOpen}
              applicationId={selectedApp}
              applicationName={selectedApplication?.name || ""}
            />
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <AppWindow className="w-16 h-16 text-slate-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">
                Select an Application
              </h3>
              <p className="text-slate-600 max-w-sm">
                Choose an application from the list to view and manage its pages
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}