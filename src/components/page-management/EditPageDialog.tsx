import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Edit2 } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { updatePage, selectPagesByApplicationId, type ApplicationPage } from "@/store/slices/applicationPagesSlice";
import { toast } from "sonner";

interface EditPageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  applicationId: string;
  page: ApplicationPage;
}

export default function EditPageDialog({
  open,
  onOpenChange,
  applicationId,
  page,
}: EditPageDialogProps) {
  const dispatch = useAppDispatch();
  const pages = useAppSelector(selectPagesByApplicationId(applicationId));
  const [pageName, setPageName] = useState(page.name);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = () => {
    const trimmedName = pageName.trim();

    if (!trimmedName) {
      setError("Page name is required");
      return;
    }

    if (trimmedName !== page.name) {
      const nameExists = pages.some(
        (p) => p.id !== page.id && p.name.toLowerCase() === trimmedName.toLowerCase()
      );

      if (nameExists) {
        setError(`A page with the name "${trimmedName}" already exists`);
        return;
      }
    }

    dispatch(
      updatePage({
        applicationId,
        pageId: page.id,
        updates: { name: trimmedName },
      })
    );

    toast.success(`Page renamed to "${trimmedName}" successfully`);
    handleClose();
  };

  const handleClose = () => {
    setPageName(page.name);
    setError(null);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Edit2 className="w-5 h-5 mr-2 text-primary" />
            Edit Page
          </DialogTitle>
          <DialogDescription>
            Update the name for this page. The page ID cannot be changed.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="page-name">Page Name</Label>
            <Input
              id="page-name"
              placeholder="e.g., Dashboard, Reports, Settings"
              value={pageName}
              onChange={(e) => {
                setPageName(e.target.value);
                setError(null);
              }}
              className={error ? "border-red-500" : ""}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="page-id">Page ID</Label>
            <Input
              id="page-id"
              value={page.id}
              disabled
              className="bg-slate-50"
            />
            <p className="text-xs text-slate-500">
              Page ID cannot be changed after creation.
            </p>
          </div>

          {error && (
            <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}