import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Edit2, Trash2, FileText, AlertTriangle } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectPagesByApplicationId, deletePage, type ApplicationPage } from "@/store/slices/applicationPagesSlice";
import { selectAllAssignments } from "@/store/slices/roleApplicationAssignmentsSlice";
import { toast } from "sonner";
import EditPageDialog from "./EditPageDialog";

interface PageListProps {
  applicationId: string;
}

export default function PageList({ applicationId }: PageListProps) {
  const dispatch = useAppDispatch();
  const pages = useAppSelector(selectPagesByApplicationId(applicationId));
  const assignments = useAppSelector(selectAllAssignments);
  const [editingPage, setEditingPage] = useState<ApplicationPage | null>(null);
  const [deletingPage, setDeletingPage] = useState<ApplicationPage | null>(null);

  const isPageInUse = (pageId: string) => {
    return assignments.some((assignment) => {
      if (assignment.applicationId !== applicationId) return false;
      return assignment.permissions && assignment.permissions[pageId] &&
             assignment.permissions[pageId].length > 0;
    });
  };

  const handleDeletePage = () => {
    if (deletingPage) {
      if (isPageInUse(deletingPage.id)) {
        toast.error(
          `Cannot delete "${deletingPage.name}" - it has active permissions assigned`,
          { duration: 4000 }
        );
        setDeletingPage(null);
        return;
      }

      dispatch(deletePage({ applicationId, pageId: deletingPage.id }));
      toast.success(`Page "${deletingPage.name}" deleted successfully`);
      setDeletingPage(null);
    }
  };

  const getAssignmentCount = (pageId: string) => {
    return assignments.filter((assignment) => {
      if (assignment.applicationId !== applicationId) return false;
      return assignment.permissions && assignment.permissions[pageId] &&
             assignment.permissions[pageId].length > 0;
    }).length;
  };

  if (pages.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="w-16 h-16 text-slate-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-slate-900 mb-2">No Pages Yet</h3>
        <p className="text-slate-600">
          Add your first page to start configuring permissions
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {pages.map((page) => {
          const assignmentCount = getAssignmentCount(page.id);
          const inUse = assignmentCount > 0;

          return (
            <Card key={page.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4 text-primary" />
                      <CardTitle className="text-base">{page.name}</CardTitle>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setEditingPage(page)}
                    >
                      <Edit2 className="w-4 h-4 text-slate-500 hover:text-primary" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setDeletingPage(page)}
                    >
                      <Trash2 className="w-4 h-4 text-slate-500 hover:text-red-600" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-slate-600">Page ID</span>
                  <code className="text-xs bg-slate-100 px-2 py-1 rounded">
                    {page.id}
                  </code>
                </div>
                {inUse && (
                  <div className="mt-3 pt-3 border-t">
                    <Badge variant="secondary" className="w-full justify-center">
                      Used in {assignmentCount} assignment{assignmentCount !== 1 ? 's' : ''}
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {editingPage && (
        <EditPageDialog
          open={!!editingPage}
          onOpenChange={(open) => !open && setEditingPage(null)}
          applicationId={applicationId}
          page={editingPage}
        />
      )}

      {deletingPage && (
        <Dialog open={!!deletingPage} onOpenChange={(open) => !open && setDeletingPage(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2 text-amber-500" />
                Delete Page
              </DialogTitle>
              <DialogDescription>
                {isPageInUse(deletingPage.id) ? (
                  <div className="space-y-2">
                    <p>
                      The page "{deletingPage.name}" cannot be deleted because it has active
                      permissions assigned.
                    </p>
                    <p className="text-amber-600 font-medium">
                      Please remove all permissions for this page before deleting it.
                    </p>
                  </div>
                ) : (
                  <p>
                    Are you sure you want to delete the page "{deletingPage.name}"? This
                    action cannot be undone.
                  </p>
                )}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeletingPage(null)}>
                Cancel
              </Button>
              {!isPageInUse(deletingPage.id) && (
                <Button variant="destructive" onClick={handleDeletePage}>
                  Delete Page
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}