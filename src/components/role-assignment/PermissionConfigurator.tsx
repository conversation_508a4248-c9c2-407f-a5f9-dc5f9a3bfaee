import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Users } from "lucide-react";
import { cn } from "@/lib/utils";
import { CRUD_OPERATIONS, CRUD_LABELS, CRUD_COLORS } from "@/constants/permissions";
import { calculatePermissionSummary } from "@/utils/permissions";

interface PagePermissions {
  pageId: string;
  pageName: string;
  permissions: {
    [roleId: string]: string[];
  };
}

interface PermissionConfiguratorProps {
  pagePermissions: PagePermissions[];
  selectedRole: string;
  roleName: string;
  onPermissionChange: (pageId: string, operation: string, checked: boolean) => void;
  viewMode?: "grid" | "compact";
  onViewModeChange?: (mode: "grid" | "compact") => void;
  showOnlyConfigured?: boolean;
  onShowOnlyConfiguredChange?: (value: boolean) => void;
}

export const PermissionConfigurator = ({
  pagePermissions,
  selectedRole,
  roleName,
  onPermissionChange,
  viewMode = "grid",
  onViewModeChange,
  showOnlyConfigured = false,
  onShowOnlyConfiguredChange,
}: PermissionConfiguratorProps) => {
  const [localViewMode, setLocalViewMode] = useState(viewMode);
  const [localShowOnlyConfigured, setLocalShowOnlyConfigured] = useState(showOnlyConfigured);

  const handleViewModeChange = (mode: "grid" | "compact") => {
    setLocalViewMode(mode);
    onViewModeChange?.(mode);
  };

  const handleShowOnlyConfiguredChange = (value: boolean) => {
    setLocalShowOnlyConfigured(value);
    onShowOnlyConfiguredChange?.(value);
  };

  const summary = calculatePermissionSummary(pagePermissions);
  
  const filteredPages = localShowOnlyConfigured
    ? pagePermissions.filter((page) => 
        Object.values(page.permissions).some((perms) => perms.length > 0)
      )
    : pagePermissions;

  return (
    <div className="space-y-6">
      {summary.totalPermissions > 0 && (
        <div className="flex items-center space-x-6 text-sm">
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4 text-slate-400" />
            <span className="text-slate-600">Role: {roleName}</span>
          </div>
          {CRUD_OPERATIONS.map((op) => (
            <div key={op} className="flex items-center space-x-2">
              <div className={cn("w-3 h-3 rounded", CRUD_COLORS[op])} />
              <span className="text-slate-600">
                {summary.byOperation[op]} {CRUD_LABELS[op]}
              </span>
            </div>
          ))}
        </div>
      )}

      <div className="p-4 border rounded-lg bg-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-slate-900">Page Permissions</h3>
            <p className="text-sm text-slate-600 mt-1">
              Configure Create, Read, Update, Delete & Execute permissions for each page
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="show-configured"
                checked={localShowOnlyConfigured}
                onCheckedChange={handleShowOnlyConfiguredChange}
              />
              <Label htmlFor="show-configured" className="text-sm">
                Show only configured
              </Label>
            </div>

            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={localViewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => handleViewModeChange("grid")}
              >
                Grid
              </Button>
              <Button
                variant={localViewMode === "compact" ? "default" : "ghost"}
                size="sm"
                onClick={() => handleViewModeChange("compact")}
              >
                Compact
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div
        className={cn(
          "grid gap-4",
          localViewMode === "grid" 
            ? "grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3" 
            : "grid-cols-1"
        )}
      >
        {filteredPages.map((page) => (
          <Card key={page.pageId} className="hover:shadow-sm transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">{page.pageName}</CardTitle>
                <div className="text-xs text-slate-500">
                  {(page.permissions[selectedRole] || []).length} permissions
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {CRUD_OPERATIONS.map((operation) => (
                  <div key={operation} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={cn("w-2 h-2 rounded", CRUD_COLORS[operation])} />
                      <Label className="font-medium">{CRUD_LABELS[operation]}</Label>
                    </div>
                    <Checkbox
                      checked={(page.permissions[selectedRole] || []).includes(operation)}
                      onCheckedChange={(checked) =>
                        onPermissionChange(page.pageId, operation, checked as boolean)
                      }
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};