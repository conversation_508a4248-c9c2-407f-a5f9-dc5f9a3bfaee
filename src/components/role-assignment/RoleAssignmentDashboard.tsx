import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Shield, Key, AppWindow, Plus, Edit2, Trash2, Settings, Search } from "lucide-react";
import { toast } from "sonner";
import type { RoleApplicationAssignment } from "@/store/slices/roleApplicationAssignmentsSlice";
import { getPermissionCount } from "@/utils/permissions";

interface RoleAssignmentDashboardProps {
  assignments: RoleApplicationAssignment[];
  onCreateNew: () => void;
  onEdit: (assignment: RoleApplicationAssignment) => void;
  onConfigure: (assignment: RoleApplicationAssignment) => void;
  onDelete: (assignmentId: string) => void;
}

export const RoleAssignmentDashboard = ({
  assignments,
  onCreateNew,
  onEdit,
  onConfigure,
  onDelete,
}: RoleAssignmentDashboardProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredAssignments = assignments.filter(
    (assignment) =>
      assignment.roleName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assignment.applicationName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = (assignmentId: string) => {
    onDelete(assignmentId);
    toast.success("Assignment deleted successfully");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center">
            <Shield className="w-7 h-7 mr-3 text-primary" />
            Role Application Assignment
          </h1>
          <p className="text-slate-600 mt-1">Manage role permissions for applications</p>
        </div>
        <Button onClick={onCreateNew}>
          <Plus className="w-4 h-4 mr-2" />
          Create New Assignment
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
        <Input
          type="text"
          placeholder="Search assignments..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredAssignments.map((assignment) => {
          const permissionCount = getPermissionCount(assignment.permissions);
          const pageCount = Object.keys(assignment.permissions).length;

          return (
            <Card key={assignment.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Key className="w-4 h-4 text-primary" />
                      </div>
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <AppWindow className="w-4 h-4 text-primary" />
                      </div>
                    </div>
                    <CardTitle className="text-lg">{assignment.roleName}</CardTitle>
                    <CardDescription className="text-sm mt-1">
                      {assignment.applicationName}
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(assignment.id)}
                  >
                    <Trash2 className="w-4 h-4 text-slate-500 hover:text-red-600" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-500">Pages Configured</span>
                    <Badge variant="secondary">{pageCount} pages</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-500">Total Permissions</span>
                    <Badge variant="secondary">{permissionCount} permissions</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-500">Last Modified</span>
                    <span className="font-medium">{assignment.modifiedAt}</span>
                  </div>
                  <div className="pt-3 flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1" 
                      onClick={() => onEdit(assignment)}
                    >
                      <Edit2 className="w-3 h-3 mr-1" />
                      Edit
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1" 
                      onClick={() => onConfigure(assignment)}
                    >
                      <Settings className="w-3 h-3 mr-1" />
                      Configure
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredAssignments.length === 0 && (
        <div className="text-center py-12">
          <Shield className="w-16 h-16 text-slate-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 mb-2">No Assignments Found</h3>
          <p className="text-slate-600 mb-6">
            {searchTerm
              ? "No assignments match your search criteria"
              : "Get started by creating your first role-application assignment"}
          </p>
          {!searchTerm && (
            <Button onClick={onCreateNew}>
              <Plus className="w-4 h-4 mr-2" />
              Create First Assignment
            </Button>
          )}
        </div>
      )}
    </div>
  );
};