import { useState } from "react";
import { MoreVertical, Search, Filter, ChevronDown, Shield, Edit, Trash2, Users, Building, FolderOpen, AppWindow } from "lucide-react";
import Avatar from "../common/Avatar";
import StatusBadge from "../common/StatusBadge";
import Button from "../common/Button";
import TruncatedContent from "../common/TruncatedContent";
import type { User } from "../../types";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllUsers, deleteUser } from "@/store/slices/usersSlice";
import { selectAllRoles } from "@/store/slices/rolesSlice";
import { selectAllGroups } from "@/store/slices/groupsSlice";
import { selectAllAssignments } from "@/store/slices/roleApplicationAssignmentsSlice";
import { selectAllPropertyGroups } from "@/store/slices/propertyGroupsSlice";
import { properties } from "@/data/properties";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import AccessManagementWizard from "../modals/AccessManagementWizard";
import { toast } from "sonner";

export default function UserTable() {
  const users = useAppSelector(selectAllUsers);
  const roles = useAppSelector(selectAllRoles);
  const groups = useAppSelector(selectAllGroups);
  const assignments = useAppSelector(selectAllAssignments);
  const propertyGroups = useAppSelector(selectAllPropertyGroups);
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [accessFilter, setAccessFilter] = useState("all");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showAccessWizard, setShowAccessWizard] = useState(false);
  const [selectedUserForAccess, setSelectedUserForAccess] = useState<User | null>(null);

  const filteredUsers = users.filter((user) => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || user.status === statusFilter;

    // Apply access filter
    let matchesAccess = true;
    if (accessFilter === "no-roles") {
      matchesAccess = (!user.groups || user.groups.length === 0) && (!user.roles || user.roles.length === 0);
    } else if (accessFilter === "no-properties") {
      matchesAccess = (!user.propertyGroups || user.propertyGroups.length === 0) && (!user.properties || user.properties.length === 0);
    } else if (accessFilter === "no-access") {
      matchesAccess =
        (!user.groups || user.groups.length === 0) &&
        (!user.roles || user.roles.length === 0) &&
        (!user.propertyGroups || user.propertyGroups.length === 0) &&
        (!user.properties || user.properties.length === 0);
    } else if (accessFilter === "fully-configured") {
      matchesAccess =
        ((user.groups?.length ?? 0) > 0 || (user.roles?.length ?? 0) > 0) &&
        ((user.propertyGroups?.length ?? 0) > 0 || (user.properties?.length ?? 0) > 0);
    }

    return matchesSearch && matchesStatus && matchesAccess;
  });

  const handleSelectAll = (checked: boolean) => {
    setSelectedUsers(checked ? filteredUsers.map((u) => u.id) : []);
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    setSelectedUsers((prev) => (checked ? [...prev, userId] : prev.filter((id) => id !== userId)));
  };

  const getUserRoles = (user: User) => {
    const userRoles = user.roles || [];
    return userRoles.map((roleId) => {
      const role = roles.find((r) => r.id === roleId);
      return role ? role.name : roleId;
    });
  };

  const getUserGroups = (user: User) => {
    const userGroups = user.groups || [];
    return userGroups.map((groupId) => {
      const group = groups.find((g) => g.id === groupId);
      return group ? group.name : groupId;
    });
  };

  const getUserApplications = (user: User) => {
    const userGroups = user.groups || [];
    const applications = new Set<string>();

    // Get applications from groups' role-application assignments
    userGroups.forEach((groupId) => {
      const group = groups.find((g) => g.id === groupId);
      if (group && group.assignmentIds) {
        group.assignmentIds.forEach((assignmentId) => {
          const assignment = assignments.find((a) => a.id === assignmentId);
          if (assignment) {
            applications.add(assignment.applicationName);
          }
        });
      }
    });

    // Get applications from individual roles' assignments
    const userRoles = user.roles || [];
    userRoles.forEach((roleId) => {
      const roleAssignments = assignments.filter((a) => a.roleId === roleId);
      roleAssignments.forEach((assignment) => {
        applications.add(assignment.applicationName);
      });
    });

    return Array.from(applications);
  };

  const getUserPropertyGroups = (user: User) => {
    const userPropertyGroups = user.propertyGroups || [];
    return userPropertyGroups.map((pgId) => {
      const pg = propertyGroups.find((g) => g.id === pgId);
      return pg ? pg.name : pgId;
    });
  };

  const getUserProperties = (user: User) => {
    const userProperties = user.properties || [];
    return userProperties.map((propId) => {
      const prop = properties.find((p) => p.id === propId);
      return prop ? prop.name : propId;
    });
  };

  const handleManageAccess = (user: User) => {
    setSelectedUserForAccess(user);
    setShowAccessWizard(true);
  };

  const handleDeleteUser = (userId: string) => {
    if (window.confirm("Are you sure you want to delete this user?")) {
      dispatch(deleteUser(userId));
      toast.success("User deleted successfully");
    }
  };

  return (
    <div className="wb-card">
      <div className="p-6 border-b border-wb-gray-200">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-wb-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-wb-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
            />
          </div>
          <div className="flex items-center space-x-3">
            <div className="relative">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="appearance-none bg-white border border-wb-gray-300 rounded-lg px-3 py-2 pr-8 text-sm focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-wb-gray-400 w-4 h-4 pointer-events-none" />
            </div>
            <div className="relative">
              <select
                value={accessFilter}
                onChange={(e) => setAccessFilter(e.target.value)}
                className="appearance-none bg-white border border-wb-gray-300 rounded-lg px-3 py-2 pr-8 text-sm focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
              >
                <option value="all">All Access</option>
                <option value="no-roles">No Roles Assigned</option>
                <option value="no-properties">No Properties Assigned</option>
                <option value="no-access">No Access</option>
                <option value="fully-configured">Fully Configured</option>
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-wb-gray-400 w-4 h-4 pointer-events-none" />
            </div>
            <Button size="sm" variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              More Filters
            </Button>
          </div>
        </div>

        {selectedUsers.length > 0 && (
          <div className="mt-4 p-3 bg-primary/5 rounded-lg flex items-center justify-between">
            <span className="text-sm font-medium text-primary/90">
              {selectedUsers.length} user{selectedUsers.length > 1 ? "s" : ""} selected
            </span>
            <div className="flex space-x-2">
              <Button size="sm" variant="primary">
                Activate
              </Button>
              <Button size="sm" variant="secondary">
                Deactivate
              </Button>
              <Button size="sm" variant="outline">
                Assign Role
              </Button>
            </div>
          </div>
        )}
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-wb-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="w-4 h-4 text-primary bg-white border-wb-gray-300 rounded focus:ring-primary/50"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider">User</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider max-w-xs">Groups & Roles</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider max-w-xs">Properties</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider max-w-xs">Applications</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-wb-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-wb-gray-200">
            {filteredUsers.map((user) => (
              <tr key={user.id} className="hover:bg-wb-gray-50 h-16">
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedUsers.includes(user.id)}
                    onChange={(e) => handleSelectUser(user.id, e.target.checked)}
                    className="w-4 h-4 text-primary bg-white border-wb-gray-300 rounded focus:ring-primary/50"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Avatar name={user.name} size="sm" className="mr-3" />
                    <div>
                      <div className="text-sm font-medium text-wb-gray-900">{user.name}</div>
                      <div className="text-sm text-wb-gray-500">{user.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 max-w-xs">
                  <TruncatedContent maxHeight={40} className="space-y-2">
                    {getUserGroups(user).length > 0 && (
                      <div>
                        <div className="flex flex-wrap gap-1">
                          {getUserGroups(user).map((groupName, idx) => (
                            <span
                              key={idx}
                              className="inline-flex items-center px-2 py-0.5 rounded text-[10px] font-medium bg-blue-100 text-blue-800"
                            >
                              <Users className="w-3 h-3 mr-1" />
                              {groupName}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {getUserRoles(user).length > 0 && (
                      <div>
                        <div className="flex flex-wrap gap-1">
                          {getUserRoles(user).map((roleName, idx) => (
                            <span
                              key={idx}
                              className="inline-flex items-center px-2 py-0.5 rounded text-[10px] font-medium bg-purple-100 text-purple-800"
                            >
                              <Shield className="w-3 h-3 mr-1" />
                              {roleName}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {getUserGroups(user).length === 0 && getUserRoles(user).length === 0 && (
                      <span className="text-xs text-wb-gray-400 italic">No access assigned</span>
                    )}
                  </TruncatedContent>
                </td>
                <td className="px-6 py-4 max-w-xs">
                  <TruncatedContent maxHeight={40} className="space-y-2">
                    {getUserPropertyGroups(user).length > 0 && (
                      <div>
                        <div className="flex flex-wrap gap-1">
                          {getUserPropertyGroups(user).map((pgName, idx) => (
                            <span
                              key={idx}
                              className="inline-flex items-center px-2 py-0.5 rounded text-[10px] font-medium bg-green-100 text-green-800"
                            >
                              <FolderOpen className="w-3 h-3 mr-1" />
                              {pgName}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {getUserProperties(user).length > 0 && (
                      <div>
                        <div className="flex flex-wrap gap-1">
                          {getUserProperties(user).map((propName, idx) => (
                            <span
                              key={idx}
                              className="inline-flex items-center px-2 py-0.5 rounded text-[10px] font-medium bg-gray-100 text-gray-700"
                            >
                              <Building className="w-3 h-3 mr-1" />
                              {propName}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {getUserPropertyGroups(user).length === 0 && getUserProperties(user).length === 0 && (
                      <span className="text-xs text-wb-gray-400 italic">No properties assigned</span>
                    )}
                  </TruncatedContent>
                </td>
                <td className="px-6 py-4 max-w-xs">
                  <TruncatedContent maxHeight={40} className="flex flex-wrap gap-1">
                    {getUserApplications(user).length > 0 ? (
                      getUserApplications(user).map((appName, idx) => (
                        <span
                          key={idx}
                          className="inline-flex items-center px-2 py-0.5 rounded text-[10px] font-medium bg-orange-100 text-orange-800"
                        >
                          <AppWindow className="w-3 h-3 mr-1" />
                          {appName}
                        </span>
                      ))
                    ) : (
                      <span className="text-xs text-wb-gray-400 italic">No apps assigned</span>
                    )}
                  </TruncatedContent>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <StatusBadge status={user.status} />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button className="text-wb-gray-400 hover:text-wb-gray-600 focus:outline-none">
                        <MoreVertical className="w-5 h-5" />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleManageAccess(user)}>
                        <Shield className="w-4 h-4 mr-2" />
                        Manage Access
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit User
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600 focus:text-red-600" onClick={() => handleDeleteUser(user.id)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete User
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredUsers.length === 0 && (
        <div className="text-center py-12">
          <div className="text-wb-gray-400 mb-4">
            <Search className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-wb-gray-900 mb-2">No users found</h3>
          <p className="text-wb-gray-500">Try adjusting your search or filter criteria.</p>
        </div>
      )}

      {selectedUserForAccess && (
        <AccessManagementWizard
          open={showAccessWizard}
          onClose={() => {
            setShowAccessWizard(false);
            setSelectedUserForAccess(null);
          }}
          user={selectedUserForAccess}
        />
      )}
    </div>
  );
}
