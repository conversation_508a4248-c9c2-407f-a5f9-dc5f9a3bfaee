import type { ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface WizardLayoutProps {
  children: ReactNode;
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onPrevious: () => void;
  onComplete?: () => void;
  nextLabel?: string;
  previousLabel?: string;
  completeLabel?: string;
  isNextDisabled?: boolean;
  isPreviousDisabled?: boolean;
  className?: string;
  footerClassName?: string;
  contentClassName?: string;
}

export const WizardLayout = ({
  children,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onComplete,
  nextLabel = "Next",
  previousLabel = "Back",
  completeLabel = "Complete",
  isNextDisabled = false,
  isPreviousDisabled = false,
  className,
  footerClassName,
  contentClassName,
}: WizardLayoutProps) => {
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;

  return (
    <div className={cn("flex flex-col", className)}>
      <div className={cn("flex-1", contentClassName)}>{children}</div>

      <div
        className={cn(
          "flex justify-between items-center mt-6 pt-6 border-t",
          footerClassName
        )}
      >
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isFirstStep || isPreviousDisabled}
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          {previousLabel}
        </Button>

        {isLastStep && onComplete ? (
          <Button onClick={onComplete} disabled={isNextDisabled}>
            {completeLabel}
          </Button>
        ) : (
          <Button onClick={onNext} disabled={isNextDisabled}>
            {nextLabel}
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        )}
      </div>
    </div>
  );
};