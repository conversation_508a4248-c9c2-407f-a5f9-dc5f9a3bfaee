import { ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface WizardStep {
  id: string | number;
  label: string;
}

interface WizardStepIndicatorProps {
  steps: WizardStep[];
  currentStep: number;
  className?: string;
}

export const WizardStepIndicator = ({
  steps,
  currentStep,
  className,
}: WizardStepIndicatorProps) => {
  return (
    <div className={cn("flex items-center justify-center space-x-4", className)}>
      {steps.map((step, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === currentStep;
        const isCompleted = stepNumber < currentStep;

        return (
          <div key={step.id} className="flex items-center">
            <div
              className={cn(
                "flex items-center",
                isActive ? "text-primary" : isCompleted ? "text-primary/80" : "text-slate-400"
              )}
            >
              <div
                className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center font-semibold",
                  isActive
                    ? "bg-primary text-white"
                    : isCompleted
                    ? "bg-primary/20 text-primary"
                    : "bg-slate-200"
                )}
              >
                {stepNumber}
              </div>
              <span className="ml-2 text-sm font-medium">{step.label}</span>
            </div>
            {index < steps.length - 1 && (
              <ChevronRight className="w-5 h-5 text-slate-400 ml-4" />
            )}
          </div>
        );
      })}
    </div>
  );
};