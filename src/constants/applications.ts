export const APPLICATION_PAGES: Record<string, string[]> = {
  prism: [
    "Dashboard",
    "Properties",
    "Tenants",
    "Leases",
    "Maintenance",
    "Reports",
    "Financials",
    "Documents",
    "Settings",
  ],
  rami: [
    "Overview",
    "Assets",
    "Performance",
    "Analytics",
    "Compliance",
    "Risk Management",
    "Portfolio",
    "Transactions",
  ],
  ldr: [
    "Reports",
    "Analytics",
    "Export",
    "Schedules",
    "Departments",
    "Settings",
  ],
  erd: [
    "Deductions",
    "Employees",
    "Properties",
    "Reports",
    "Approvals",
    "Settings",
  ],
  ems: [
    "Employees",
    "Departments",
    "Roles",
    "Onboarding",
    "Performance",
    "Reports",
    "Settings",
  ],
};

export type ApplicationId = keyof typeof APPLICATION_PAGES;