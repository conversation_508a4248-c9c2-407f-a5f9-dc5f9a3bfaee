import type { AccessRequest } from '@/types';

export const accessRequests: AccessRequest[] = [
  {
    id: '1',
    userId: '1',
    userName: '<PERSON>',
    userEmail: '<EMAIL>',
    requestedRole: 'pm_user',
    requestedApplications: ['prism', 'rami'],
    requestedProperties: ['1', '3', '5'],
    reason: 'I need access to Prism and RAMI to manage property data and generate reports for the West Coast properties I oversee. This will help me track performance metrics and maintenance schedules more effectively.',
    status: 'pending',
    requestDate: '2025-01-15'
  },
  {
    id: '2',
    userId: '3',
    userName: '<PERSON>',
    userEmail: '<EMAIL>',
    requestedRole: 'lease_manager',
    requestedApplications: ['prism'],
    requestedProperties: ['2', '4'],
    reason: 'As the new lease manager for East region properties, I require access to Prism to manage tenant leases and handle resident inquiries. This is critical for my day-to-day operations.',
    status: 'pending',
    requestDate: '2025-01-16'
  },
  {
    id: '3',
    userId: '6',
    userName: '<PERSON>',
    userEmail: '<EMAIL>',
    requestedRole: 'property_manager',
    requestedApplications: ['prism', 'rami'],
    requestedProperties: ['7', '8', '9'],
    reason: 'I have been promoted to Property Manager for three commercial properties. I need full access to Prism for property management and RAMI for asset tracking and performance analytics.',
    status: 'pending',
    requestDate: '2025-01-17'
  },
  {
    id: '4',
    userId: '10',
    userName: 'Amanda White',
    userEmail: '<EMAIL>',
    requestedRole: 'financial_analyst',
    requestedApplications: ['rami', 'ldr'],
    requestedProperties: ['11', '12', '13'],
    reason: 'I need access to RAMI for financial analysis and the Labor Distribution Report system to generate payroll analytics. This will help me prepare quarterly reports and analyze property performance.',
    status: 'pending',
    requestDate: '2025-01-18'
  },
  {
    id: '5',
    userId: '13',
    userName: 'Thomas Miller',
    userEmail: '<EMAIL>',
    requestedRole: 'hr_manager',
    requestedApplications: ['ems', 'erd', 'ldr'],
    requestedProperties: ['14', '15'],
    reason: 'As HR Manager, I need access to the Employee Management System for staff administration, Employee Rent Deduction for housing benefits management, and Labor Distribution Report for payroll analysis.',
    status: 'pending',
    requestDate: '2025-01-19'
  },
  {
    id: '6',
    userId: '17',
    userName: 'Christopher Jones',
    userEmail: '<EMAIL>',
    requestedRole: 'accounting_clerk',
    requestedApplications: ['ldr', 'erd'],
    requestedProperties: ['1', '2'],
    reason: 'I need access to Labor Distribution Report for payroll processing and Employee Rent Deduction system to manage employee housing deductions. This is essential for accurate accounting and payroll operations.',
    status: 'pending',
    requestDate: '2025-01-20'
  }
];