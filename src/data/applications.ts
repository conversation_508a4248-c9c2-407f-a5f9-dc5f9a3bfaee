import type { Application } from '@/types';

export const applications: Application[] = [
  {
    id: 'prism',
    name: 'Prism',
    description: 'Property Management System'
  },
  {
    id: 'rami',
    name: 'RAMI',
    description: 'Resident and Asset Management Interface'
  },
  {
    id: 'ldr',
    name: 'Labor Distribution Report',
    description: 'Payroll and Labor Analytics System'
  },
  {
    id: 'erd',
    name: 'Employee Rent Deduction',
    description: 'Employee Housing and Rent Management'
  },
  {
    id: 'ems',
    name: 'Employee Management System',
    description: 'HR and Employee Administration Platform'
  }
];