import type { Group } from '@/types';

export const groups: Group[] = [
  {
    id: '1',
    name: 'Property Managers',
    description: 'Full property management access and permissions',
    memberCount: 12,
    members: ['2', '5', '11', '14'],
    permissions: ['view_property', 'edit_property', 'view_tenants', 'edit_tenants'],
    assignmentIds: []
  },
  {
    id: '2',
    name: 'Leasing Team',
    description: 'Leasing and tenant management access',
    memberCount: 8,
    members: ['4', '12'],
    permissions: ['view_leasing', 'edit_leasing', 'view_tenants'],
    assignmentIds: []
  },
  {
    id: '3',
    name: 'Maintenance Crew',
    description: 'Maintenance and work order access',
    memberCount: 15,
    members: ['7'],
    permissions: ['view_maintenance', 'edit_maintenance', 'view_property'],
    assignmentIds: []
  },
  {
    id: '4',
    name: 'Financial Team',
    description: 'Financial reporting and analysis access',
    memberCount: 6,
    members: ['8', '15'],
    permissions: ['view_financials', 'edit_financials', 'view_reports'],
    assignmentIds: []
  },
  {
    id: '5',
    name: 'Executives',
    description: 'Full system access for executive team',
    memberCount: 4,
    members: ['14'],
    permissions: ['full_access'],
    assignmentIds: []
  },
  {
    id: '6',
    name: 'Regional Managers',
    description: 'Regional oversight and management',
    memberCount: 5,
    members: ['5'],
    permissions: ['view_region', 'edit_region', 'view_reports'],
    assignmentIds: []
  },
  {
    id: '7',
    name: 'Guest Viewers',
    description: 'Limited read-only access for external stakeholders',
    memberCount: 20,
    members: ['9'],
    permissions: ['view_limited'],
    assignmentIds: []
  },
  {
    id: '8',
    name: 'HR Team',
    description: 'Human resources and staff management',
    memberCount: 3,
    members: [],
    permissions: ['view_hr', 'edit_hr', 'view_users'],
    assignmentIds: []
  },
  {
    id: '9',
    name: 'IT Support',
    description: 'Technical support and system administration',
    memberCount: 4,
    members: [],
    permissions: ['view_it', 'edit_it', 'view_audit'],
    assignmentIds: []
  },
  {
    id: '10',
    name: 'Compliance Officers',
    description: 'Regulatory compliance and audit management',
    memberCount: 2,
    members: [],
    permissions: ['view_compliance', 'edit_compliance', 'view_audit'],
    assignmentIds: []
  }
];