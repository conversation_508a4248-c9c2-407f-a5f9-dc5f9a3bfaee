export interface PagePermission {
  pageId: string;
  pageName: string;
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
}

export interface ApplicationPermission {
  applicationId: string;
  applicationName: string;
  pages: PagePermission[];
}

export const applicationPages: Record<string, { id: string; name: string }[]> = {
  prism: [
    { id: 'dashboard', name: 'Dashboard' },
    { id: 'properties', name: 'Properties' },
    { id: 'tenants', name: 'Tenants' },
    { id: 'leases', name: 'Leases' },
    { id: 'maintenance', name: 'Maintenance' },
    { id: 'reports', name: 'Reports' },
    { id: 'financials', name: 'Financials' },
    { id: 'documents', name: 'Documents' },
    { id: 'settings', name: 'Setting<PERSON>' },
  ],
  rami: [
    { id: 'overview', name: 'Overview' },
    { id: 'assets', name: 'Assets' },
    { id: 'performance', name: 'Performance' },
    { id: 'analytics', name: 'Analytics' },
    { id: 'compliance', name: 'Compliance' },
    { id: 'risk_management', name: 'Risk Management' },
    { id: 'portfolio', name: 'Portfolio' },
    { id: 'transactions', name: 'Transactions' },
  ],
  ldr: [
    { id: 'reports', name: 'Reports' },
    { id: 'analytics', name: 'Analytics' },
    { id: 'export', name: 'Export' },
    { id: 'schedules', name: 'Schedules' },
    { id: 'departments', name: 'Departments' },
    { id: 'settings', name: 'Settings' },
  ],
  erd: [
    { id: 'deductions', name: 'Deductions' },
    { id: 'employees', name: 'Employees' },
    { id: 'properties', name: 'Properties' },
    { id: 'reports', name: 'Reports' },
    { id: 'approvals', name: 'Approvals' },
    { id: 'settings', name: 'Settings' },
  ],
  ems: [
    { id: 'employees', name: 'Employees' },
    { id: 'departments', name: 'Departments' },
    { id: 'roles', name: 'Roles' },
    { id: 'onboarding', name: 'Onboarding' },
    { id: 'performance', name: 'Performance' },
    { id: 'reports', name: 'Reports' },
    { id: 'settings', name: 'Settings' },
  ],
};

export const permissionOperations = ['create', 'read', 'update', 'delete'] as const;
export type PermissionOperation = typeof permissionOperations[number];

export const getDefaultPagePermissions = (pageId: string, pageName: string): PagePermission => ({
  pageId,
  pageName,
  create: false,
  read: false,
  update: false,
  delete: false,
});

export const getDefaultApplicationPermissions = (applicationId: string, applicationName: string): ApplicationPermission => ({
  applicationId,
  applicationName,
  pages: (applicationPages[applicationId] || []).map(page => 
    getDefaultPagePermissions(page.id, page.name)
  ),
});