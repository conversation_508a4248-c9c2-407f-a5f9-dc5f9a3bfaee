import type { Property } from '@/types';

export const properties: Property[] = [
  { id: '1', name: '1200M', region: 'West', type: 'Multi-family' },
  { id: '2', name: '1212 Lofts', region: 'East', type: 'Luxury Lofts' },
  { id: '3', name: '1325 Jefferson', region: 'East', type: 'Apartment Complex' },
  { id: '4', name: '1200 Broadway', region: 'East', type: 'Mixed-use' },
  { id: '5', name: '1940 Green Randolph Street Lofts', region: 'West', type: 'Urban Lofts' },
  { id: '6', name: '1407 On Michigan', region: 'West', type: 'High-rise' },
  { id: '7', name: '16 Twenty', region: 'West', type: 'Modern Apartments' },
  { id: '8', name: '1820 Lake', region: 'West', type: 'Lakefront Property' },
  { id: '9', name: '1808 Edgehill', region: 'East', type: 'Garden Apartments' },
  { id: '10', name: '180 North Jefferson', region: 'East', type: 'Downtown Tower' },
  { id: '11', name: '1901 South Charles Apartments', region: 'East', type: 'Historic Building' },
  { id: '12', name: '215 West Washington', region: 'East', type: 'Corporate Housing' },
  { id: '13', name: 'Sunset Ridge Apartments', region: 'West', type: 'Suburban Complex' },
  { id: '14', name: 'Marina Bay Towers', region: 'West', type: 'Waterfront High-rise' },
  { id: '15', name: 'Heritage Park Commons', region: 'East', type: 'Family Community' }
];