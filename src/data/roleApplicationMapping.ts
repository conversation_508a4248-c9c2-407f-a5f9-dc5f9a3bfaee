// Mapping of roles to their primary applications
export const roleApplicationMapping: Record<string, string[]> = {
  // Property Management roles
  pm_user: ["prism"],
  property_manager: ["prism", "rami"],
  lease_manager: ["prism"],
  resident_manager: ["prism"],
  maintenance_tech: ["prism"],
  leasing_agent: ["prism"],
  facilities_manager: ["prism"],

  // Asset Management roles
  asset_manager: ["rami"],
  portfolio_analyst: ["rami"],
  financial_analyst: ["rami", "ldr", "erd"],
  risk_analyst: ["rami"],

  // HR and Finance roles
  accounting_clerk: ["ldr", "erd"],
  insurance_coord: ["erd"],
  hr_manager: ["ems", "erd", "ldr"],
  hr_specialist: ["ems", "erd"],

  // Cross-application roles
  admin: ["prism", "rami", "ldr", "erd", "ems"],
  viewer: ["prism", "rami", "ldr", "erd", "ems"],
  regional_manager: ["prism", "rami", "ldr"],
  executive: ["prism", "rami", "ldr", "ems"],
  compliance_officer: ["prism", "rami", "ldr", "erd", "ems"],
  audit_manager: ["prism", "rami", "ldr", "erd"],

  // Support roles
  it_support: ["prism", "rami", "ldr", "erd", "ems"],
  legal_counsel: ["prism", "rami"],

  // Specialized roles
  development_manager: ["prism", "rami"],
  marketing_coord: ["prism"],
  security_manager: ["prism"],
  data_analyst: ["rami", "ldr"],
  training_coord: ["prism", "ems"],
  project_manager: ["prism", "rami"],
  business_analyst: ["rami", "ldr"],
  quality_assurance: ["prism", "rami", "ldr", "erd", "ems"],

  // Additional roles
  vendor_manager: ["prism"],
  tenant_relations: ["prism"],
  guest_user: ["prism"],
};

// Application display names
export const applicationDisplayNames: Record<string, string> = {
  prism: "Prism",
  rami: "RAMI",
  ldr: "Labor Distribution Report",
  erd: "Employee Rent Deduction",
  ems: "Employee Management System",
};