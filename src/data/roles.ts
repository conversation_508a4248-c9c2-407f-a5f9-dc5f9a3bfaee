import type { Role } from "@/types";

export const roles: Role[] = [
  { id: "mdp_admin", name: "MDP Admin", description: "Master Data Platform Administrator", permissions: ["full_access"] },
  { id: "sec_admin", name: "Security Admin", description: "Security Administrator", permissions: ["manage_security", "view_audit", "manage_users"] },
  { id: "ro_sup", name: "Read Only Super User", description: "Read-only access to all resources", permissions: ["view_all"] },
  { id: "it_sup", name: "IT Super User", description: "IT department super user", permissions: ["manage_it", "view_it", "edit_it"] },
  { id: "it_user", name: "IT User", description: "IT department user", permissions: ["view_it", "edit_it"] },
  { id: "app_admin", name: "Application Admin", description: "Application Administrator", permissions: ["manage_applications", "view_applications"] },
  { id: "elt_user", name: "ELT User", description: "Extract, Load, Transform User", permissions: ["view_elt", "edit_elt", "execute_elt"] },
  {
    id: "audit_sup",
    name: "Audit Super User",
    description: "Audit department super user",
    permissions: ["view_audit", "edit_audit", "execute_audit"],
  },
  { id: "audit_user", name: "Audit User", description: "Audit department user", permissions: ["view_audit"] },
  {
    id: "training_sup",
    name: "Training Super User",
    description: "Training department super user",
    permissions: ["manage_training", "view_training", "edit_training"],
  },
  { id: "training_user", name: "Training User", description: "Training department user", permissions: ["view_training"] },
  {
    id: "td_sup",
    name: "Talent Development Super User",
    description: "Talent Development super user",
    permissions: ["manage_talent", "view_talent", "edit_talent"],
  },
  { id: "td_user", name: "Talent Development User", description: "Talent Development user", permissions: ["view_talent"] },
  {
    id: "mc_sup",
    name: "Marketing & Communications Super User",
    description: "Marketing & Communications super user",
    permissions: ["manage_marketing", "view_marketing", "edit_marketing"],
  },
  {
    id: "mc_user",
    name: "Marketing & Communications User",
    description: "Marketing & Communications user",
    permissions: ["view_marketing", "edit_marketing"],
  },
  {
    id: "inv_sup",
    name: "Investments Super User",
    description: "Investments department super user",
    permissions: ["manage_investments", "view_investments", "edit_investments"],
  },
  { id: "inv_user", name: "Investments User", description: "Investments department user", permissions: ["view_investments"] },
  {
    id: "dc_sup",
    name: "Development & Construction Super User",
    description: "Development & Construction super user",
    permissions: ["manage_development", "view_development", "edit_development"],
  },
  {
    id: "dc_user",
    name: "Development & Construction User",
    description: "Development & Construction user",
    permissions: ["view_development", "edit_development"],
  },
  {
    id: "acct_sup",
    name: "Accounting Super User",
    description: "Accounting department super user",
    permissions: ["manage_accounting", "view_accounting", "edit_accounting"],
  },
  { id: "acct_user", name: "Accounting User", description: "Accounting department user", permissions: ["view_accounting", "edit_accounting"] },
  {
    id: "fpa_sup",
    name: "FP & A Super User",
    description: "Financial Planning & Analysis super user",
    permissions: ["manage_fpa", "view_fpa", "edit_fpa"],
  },
  { id: "fpa_user", name: "FP & A User", description: "Financial Planning & Analysis user", permissions: ["view_fpa", "edit_fpa"] },
  {
    id: "aso_sup",
    name: "Asset Strategy & Optimization Super User",
    description: "Asset Strategy & Optimization super user",
    permissions: ["manage_assets", "view_assets", "edit_assets"],
  },
  {
    id: "aso_user",
    name: "Asset Strategy & Optimization User",
    description: "Asset Strategy & Optimization user",
    permissions: ["view_assets", "edit_assets"],
  },
  {
    id: "cs_sup",
    name: "Client Services Super User",
    description: "Client Services super user",
    permissions: ["manage_clients", "view_clients", "edit_clients"],
  },
  { id: "cs_user", name: "Client Services User", description: "Client Services user", permissions: ["view_clients", "edit_clients"] },
  {
    id: "legal_sup",
    name: "Legal Super User",
    description: "Legal department super user",
    permissions: ["manage_legal", "view_legal", "edit_legal"],
  },
  { id: "legal_user", name: "Legal User", description: "Legal department user", permissions: ["view_legal", "edit_legal"] },
  {
    id: "strategy_sup",
    name: "Corporate Strategy Super User",
    description: "Corporate Strategy super user",
    permissions: ["manage_strategy", "view_strategy", "edit_strategy"],
  },
  { id: "strategy_user", name: "Corporate Strategy User", description: "Corporate Strategy user", permissions: ["view_strategy", "edit_strategy"] },
  {
    id: "fm_sup",
    name: "Facilities & Maintenance Super User",
    description: "Facilities & Maintenance super user",
    permissions: ["manage_facilities", "view_facilities", "edit_facilities"],
  },
  {
    id: "fm_user",
    name: "Facilities & Maintenance User",
    description: "Facilities & Maintenance user",
    permissions: ["view_facilities", "edit_facilities"],
  },
  {
    id: "pm_sup",
    name: "Property Management Super User",
    description: "Property Management super user",
    permissions: ["manage_properties", "view_properties", "edit_properties"],
  },
  { id: "pm_user", name: "Property Management User", description: "Property Management user", permissions: ["view_properties", "edit_properties"] },
  { id: "mgr_user", name: "Business Manager User", description: "Business Manager user", permissions: ["view_business", "edit_business"] },
  { id: "leasing_user", name: "Leasing Agent User", description: "Leasing Agent user", permissions: ["view_leasing", "edit_leasing"] },
  {
    id: "rpm_user",
    name: "Regional Property Manager User",
    description: "Regional Property Manager user",
    permissions: ["view_region", "edit_region", "manage_region"],
  },
  {
    id: "vp_user",
    name: "PM Vice President User",
    description: "Property Management Vice President user",
    permissions: ["view_executive", "edit_executive", "manage_properties"],
  },
  {
    id: "svp_user",
    name: "PM Senior Vice President User",
    description: "Property Management Senior Vice President user",
    permissions: ["full_access", "view_executive", "edit_executive", "manage_properties"],
  },
];
