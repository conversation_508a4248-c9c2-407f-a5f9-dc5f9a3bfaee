import type { User } from "@/types";

export const users: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "pending",
    currentRole: null,
    lastLogin: null,
    requestDate: "2025-01-15",
    applications: [],
    properties: [],
    propertyGroups: [],
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    currentRole: "pm_user",
    roles: ["pm_user"],
    groups: ["grp-1"],
    lastLogin: "2025-01-14 3:45 PM",
    applications: ["prism", "rami"],
    properties: ["1", "3", "5"],
    propertyGroups: ["pg-west-1"],
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "pending",
    currentRole: null,
    lastLogin: null,
    requestDate: "2025-01-16",
    applications: [],
    properties: [],
  },
  {
    id: "4",
    name: "<PERSON>",
    email: "e.<PERSON><PERSON><PERSON><PERSON>@willowbridge.com",
    status: "active",
    currentRole: "lease_manager",
    lastLogin: "2025-01-15 10:22 AM",
    applications: ["prism"],
    properties: ["2", "4", "6"],
  },
  {
    id: "5",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    currentRole: "regional_manager",
    lastLogin: "2025-01-15 2:15 PM",
    applications: ["prism", "rami", "ldr"],
    properties: ["1", "2", "3", "4", "5"],
  },
  {
    id: "6",
    name: "Lisa Thompson",
    email: "<EMAIL>",
    status: "pending",
    currentRole: null,
    lastLogin: null,
    requestDate: "2025-01-17",
    applications: [],
    properties: [],
  },
  {
    id: "7",
    name: "Robert Anderson",
    email: "<EMAIL>",
    status: "active",
    currentRole: "maintenance_tech",
    lastLogin: "2025-01-14 4:30 PM",
    applications: ["prism"],
    properties: ["7", "8", "9"],
  },
  {
    id: "8",
    name: "Jennifer Lee",
    email: "<EMAIL>",
    status: "active",
    currentRole: "financial_analyst",
    lastLogin: "2025-01-15 9:00 AM",
    applications: ["rami", "ldr", "prism"],
    properties: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
  },
  {
    id: "9",
    name: "Mark Davis",
    email: "<EMAIL>",
    status: "inactive",
    currentRole: "viewer",
    lastLogin: "2024-12-15 1:20 PM",
    applications: ["prism"],
    properties: ["11", "12"],
  },
  {
    id: "10",
    name: "Amanda White",
    email: "<EMAIL>",
    status: "pending",
    currentRole: null,
    lastLogin: null,
    requestDate: "2025-01-18",
    applications: [],
    properties: [],
  },
  {
    id: "11",
    name: "Kevin Brown",
    email: "<EMAIL>",
    status: "active",
    currentRole: "property_manager",
    lastLogin: "2025-01-15 11:45 AM",
    applications: ["prism", "rami"],
    properties: ["13", "14", "15"],
  },
  {
    id: "12",
    name: "Rachel Green",
    email: "<EMAIL>",
    status: "active",
    currentRole: "leasing_agent",
    lastLogin: "2025-01-15 8:30 AM",
    applications: ["prism"],
    properties: ["1", "2", "3"],
  },
  {
    id: "13",
    name: "Thomas Miller",
    email: "<EMAIL>",
    status: "pending",
    currentRole: null,
    lastLogin: null,
    requestDate: "2025-01-19",
    applications: [],
    properties: [],
  },
  {
    id: "14",
    name: "Susan Taylor",
    email: "<EMAIL>",
    status: "active",
    currentRole: "admin",
    lastLogin: "2025-01-15 3:00 PM",
    applications: ["prism", "rami", "ldr", "erd", "ems"],
    properties: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"],
  },
  {
    id: "15",
    name: "James Wilson",
    email: "<EMAIL>",
    status: "active",
    currentRole: "accounting_clerk",
    lastLogin: "2025-01-14 5:15 PM",
    applications: ["ldr", "erd"],
    properties: ["4", "5", "6"],
  },
  {
    id: "16",
    name: "Maria Garcia",
    email: "<EMAIL>",
    status: "active",
    currentRole: "resident_manager",
    lastLogin: "2025-01-15 1:30 PM",
    applications: ["prism", "ems"],
    properties: ["7", "8", "9", "10"],
  },
  {
    id: "17",
    name: "Christopher Jones",
    email: "<EMAIL>",
    status: "pending",
    currentRole: null,
    lastLogin: null,
    requestDate: "2025-01-20",
    applications: [],
    properties: [],
  },
  {
    id: "18",
    name: "Michelle Clark",
    email: "<EMAIL>",
    status: "active",
    currentRole: "marketing_coord",
    lastLogin: "2025-01-15 12:00 PM",
    applications: ["prism"],
    properties: ["11", "12", "13"],
  },
];
