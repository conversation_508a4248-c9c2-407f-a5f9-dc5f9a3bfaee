import { useState, useMemo, useCallback } from "react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllRoles } from "@/store/slices/rolesSlice";
import {
  selectAllAssignments,
  addAssignment,
  updateAssignment,
  deleteAssignment,
  type RoleApplicationAssignment,
} from "@/store/slices/roleApplicationAssignmentsSlice";
import { applications } from "@/data/applications";
import { applicationPages } from "@/data/permissions";
import { toast } from "sonner";

interface PagePermissions {
  pageId: string;
  pageName: string;
  permissions: {
    [roleId: string]: string[];
  };
}

export const useRoleAssignment = () => {
  const dispatch = useAppDispatch();
  const roles = useAppSelector(selectAllRoles);
  const assignments = useAppSelector(selectAllAssignments);

  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<string | null>(null);
  const [pagePermissions, setPagePermissions] = useState<PagePermissions[]>([]);
  const [editingAssignment, setEditingAssignment] = useState<RoleApplicationAssignment | null>(null);

  const selectedRoleObj = useMemo(() => {
    return roles.find((role) => role.id === selectedRole);
  }, [roles, selectedRole]);

  const selectedAppObj = useMemo(() => {
    return applications.find((app) => app.id === selectedApplication);
  }, [selectedApplication]);

  const isAssignmentExists = useCallback(
    (roleId: string, applicationId: string) => {
      return assignments.some(
        (a) =>
          a.roleId === roleId &&
          a.applicationId === applicationId &&
          a.id !== editingAssignment?.id
      );
    },
    [assignments, editingAssignment]
  );

  const initializePermissions = useCallback(
    (
      existingPermissions?: { [pageId: string]: string[] },
      roleId?: string,
      appId?: string
    ) => {
      const role = roleId || selectedRole;
      const app = appId || selectedApplication;

      if (app && role) {
        const pages = applicationPages[app] || [];
        const initialPermissions: PagePermissions[] = pages.map((page) => ({
          pageId: page.id,
          pageName: page.name,
          permissions: {
            [role]: existingPermissions ? existingPermissions[page.id] || [] : [],
          },
        }));
        setPagePermissions(initialPermissions);
      }
    },
    [selectedRole, selectedApplication]
  );

  const handlePermissionChange = useCallback(
    (pageId: string, operation: string, checked: boolean) => {
      if (!selectedRole) return;

      setPagePermissions((prev) =>
        prev.map((page) => {
          if (page.pageId === pageId) {
            const currentPermissions = page.permissions[selectedRole] || [];
            const updatedPermissions = checked
              ? [...currentPermissions, operation]
              : currentPermissions.filter((perm) => perm !== operation);

            return {
              ...page,
              permissions: {
                ...page.permissions,
                [selectedRole]: updatedPermissions,
              },
            };
          }
          return page;
        })
      );
    },
    [selectedRole]
  );

  const saveAssignment = useCallback(() => {
    if (selectedRole && selectedApplication && selectedRoleObj && selectedAppObj) {
      if (!editingAssignment && isAssignmentExists(selectedRole, selectedApplication)) {
        toast.error(`${selectedRoleObj.name} is already configured for ${selectedAppObj.name}`);
        return false;
      }

      const permissions: { [pageId: string]: string[] } = {};
      pagePermissions.forEach((page) => {
        if (page.permissions[selectedRole] && page.permissions[selectedRole].length > 0) {
          permissions[page.pageId] = page.permissions[selectedRole];
        }
      });

      if (editingAssignment) {
        if (
          (editingAssignment.roleId !== selectedRole ||
            editingAssignment.applicationId !== selectedApplication) &&
          isAssignmentExists(selectedRole, selectedApplication)
        ) {
          toast.error(`${selectedRoleObj.name} is already configured for ${selectedAppObj.name}`);
          return false;
        }

        const updatedAssignment: RoleApplicationAssignment = {
          ...editingAssignment,
          roleId: selectedRole,
          roleName: selectedRoleObj.name,
          applicationId: selectedApplication,
          applicationName: selectedAppObj.name,
          permissions,
          modifiedAt: new Date().toISOString().split("T")[0],
        };

        dispatch(updateAssignment(updatedAssignment));
        toast.success("Assignment updated successfully");
      } else {
        const newAssignment: RoleApplicationAssignment = {
          id: `assign_${Date.now()}`,
          roleId: selectedRole,
          roleName: selectedRoleObj.name,
          applicationId: selectedApplication,
          applicationName: selectedAppObj.name,
          permissions,
          createdAt: new Date().toISOString().split("T")[0],
          modifiedAt: new Date().toISOString().split("T")[0],
        };

        dispatch(addAssignment(newAssignment));
        toast.success("Assignment created successfully");
      }

      return true;
    }
    return false;
  }, [
    selectedRole,
    selectedApplication,
    selectedRoleObj,
    selectedAppObj,
    pagePermissions,
    editingAssignment,
    isAssignmentExists,
    dispatch,
  ]);

  const deleteRoleAssignment = useCallback(
    (assignmentId: string) => {
      dispatch(deleteAssignment(assignmentId));
    },
    [dispatch]
  );

  const reset = useCallback(() => {
    setSelectedRole(null);
    setSelectedApplication(null);
    setPagePermissions([]);
    setEditingAssignment(null);
  }, []);

  return {
    roles,
    assignments,
    selectedRole,
    setSelectedRole,
    selectedApplication,
    setSelectedApplication,
    selectedRoleObj,
    selectedAppObj,
    pagePermissions,
    setPagePermissions,
    editingAssignment,
    setEditingAssignment,
    isAssignmentExists,
    initializePermissions,
    handlePermissionChange,
    saveAssignment,
    deleteRoleAssignment,
    reset,
  };
};