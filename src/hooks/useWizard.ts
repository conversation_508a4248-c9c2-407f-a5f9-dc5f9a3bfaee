import { useState, useCallback } from "react";

export interface UseWizardOptions {
  initialStep?: number;
  totalSteps: number;
  onComplete?: () => void;
}

export interface UseWizardReturn {
  currentStep: number;
  totalSteps: number;
  progress: number;
  isFirstStep: boolean;
  isLastStep: boolean;
  goToNext: () => void;
  goToPrevious: () => void;
  goToStep: (step: number) => void;
  reset: () => void;
  canGoNext: boolean;
  canGoPrevious: boolean;
}

export const useWizard = ({
  initialStep = 1,
  totalSteps,
  onComplete,
}: UseWizardOptions): UseWizardReturn => {
  const [currentStep, setCurrentStep] = useState(initialStep);

  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;
  const progress = (currentStep / totalSteps) * 100;
  const canGoNext = currentStep < totalSteps;
  const canGoPrevious = currentStep > 1;

  const goToNext = useCallback(() => {
    if (currentStep < totalSteps) {
      setCurrentStep((prev) => prev + 1);
    } else if (currentStep === totalSteps && onComplete) {
      onComplete();
    }
  }, [currentStep, totalSteps, onComplete]);

  const goToPrevious = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  const goToStep = useCallback(
    (step: number) => {
      if (step >= 1 && step <= totalSteps) {
        setCurrentStep(step);
      }
    },
    [totalSteps]
  );

  const reset = useCallback(() => {
    setCurrentStep(initialStep);
  }, [initialStep]);

  return {
    currentStep,
    totalSteps,
    progress,
    isFirstStep,
    isLastStep,
    goToNext,
    goToPrevious,
    goToStep,
    reset,
    canGoNext,
    canGoPrevious,
  };
};