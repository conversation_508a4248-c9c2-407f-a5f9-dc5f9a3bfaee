@import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));
@custom-variant light (&:not(.dark *));

@layer base {
  * {
    @apply border-border;
  }

  *:focus-visible {
    @apply outline-ring rounded-xs shadow-none outline-2 outline-offset-3 transition-none!;
  }

  html, body, * {
    font-family: 'Roboto Condensed', sans-serif !important;
  }
  
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .wb-card {
    @apply bg-white rounded-lg shadow-sm border border-slate-200;
  }

  .wb-button-primary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }

  .wb-button-secondary {
    @apply bg-white hover:bg-slate-50 text-slate-700 border border-slate-300 px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }

  .wb-input {
    @apply block w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors duration-200;
  }
}

@theme inline {

  --radius-xs: calc(var(--radius) - 4px);

  --radius-sm: calc(var(--radius) - 2px);

  --radius-md: var(--radius);

  --radius-lg: calc(var(--radius) + 2px);

  --radius-xl: calc(var(--radius) + 4px);

  --color-background: var(--background);

  --color-foreground: var(--foreground);

  --color-card: var(--card);

  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);

  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);

  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);

  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);

  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);

  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);

  --color-destructive-foreground: var(--destructive-foreground);

  --color-success: var(--success);

  --color-success-foreground: var(--success-foreground);

  --color-warning: var(--warning);

  --color-warning-foreground: var(--warning-foreground);

  --color-info: var(--info);

  --color-info-foreground: var(--info-foreground);

  --color-border: var(--border);

  --color-input: var(--input);

  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);

  --color-chart-2: var(--chart-2);

  --color-chart-3: var(--chart-3);

  --color-chart-4: var(--chart-4);

  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar);

  --color-sidebar-foreground: var(--sidebar-foreground);

  --color-sidebar-primary: var(--sidebar-primary);

  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);

  --color-sidebar-accent: var(--sidebar-accent);

  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);

  --color-sidebar-border: var(--sidebar-border);

  --color-sidebar-ring: var(--sidebar-ring);
}

:root {

  --radius: 0.5rem;

  --background: oklch(1 0 0);

  --foreground: oklch(27.4% 0.006 286.033);

  --card: oklch(1 0 0);

  --card-foreground: oklch(27.4% 0.006 286.033);

  --popover: oklch(1 0 0);

  --popover-foreground: oklch(27.4% 0.006 286.033);

  --primary: #43298F;

  --primary-foreground: oklch(1 0 0);

  --secondary: oklch(96.7% 0.003 264.542);

  --secondary-foreground: oklch(44.6% 0.03 256.802);

  --muted: oklch(96.7% 0.003 264.542);

  --muted-foreground: oklch(70.5% 0.015 286.067);

  --accent: oklch(96.7% 0.003 264.542);

  --accent-foreground: oklch(21% 0.006 285.885);

  --destructive: oklch(57.7% 0.245 27.325);

  --destructive-foreground: oklch(1 0 0);

  --success: oklch(60.8% 0.165 164.62);

  --success-foreground: oklch(1 0 0);

  --warning: oklch(75.8% 0.154 83.618);

  --warning-foreground: oklch(1 0 0);

  --info: oklch(63.9% 0.177 255.34);

  --info-foreground: oklch(1 0 0);

  --border: oklch(94% 0.004 286.32);

  --input: oklch(92% 0.004 286.32);

  --ring: #43298F;

  --chart-1: oklch(0.646 0.222 41.116);

  --chart-2: oklch(0.6 0.118 184.704);

  --chart-3: oklch(0.398 0.07 227.392);

  --chart-4: oklch(0.828 0.189 84.429);

  --chart-5: oklch(0.769 0.188 70.08);

  --sidebar: oklch(0.985 0 0);

  --sidebar-foreground: oklch(0.145 0 0);

  --sidebar-primary: #43298F;

  --sidebar-primary-foreground: oklch(0.985 0 0);

  --sidebar-accent: oklch(0.97 0 0);

  --sidebar-accent-foreground: oklch(0.205 0 0);

  --sidebar-border: oklch(0.922 0 0);

  --sidebar-ring: oklch(0.708 0 0);
}

.dark {

  --background: oklch(14.1% 0.005 285.823);

  --foreground: oklch(98.5% 0 0);

  --card: oklch(14.1% 0.005 285.823);

  --card-foreground: oklch(98.5% 0 0);

  --popover: oklch(14.1% 0.005 285.823);

  --popover-foreground: oklch(98.5% 0 0);

  --primary: #6444a8;

  --primary-foreground: oklch(1 0 0);

  --secondary: oklch(27.4% 0.006 286.033);

  --secondary-foreground: oklch(70.5% 0.015 286.067);

  --muted: oklch(21% 0.006 285.885);

  --muted-foreground: oklch(55.2% 0.016 285.938);

  --accent: oklch(21% 0.006 285.885);

  --accent-foreground: oklch(98.5% 0 0);

  --destructive: oklch(57.7% 0.245 27.325);

  --destructive-foreground: oklch(1 0 0);

  --success: oklch(60.8% 0.165 164.62);

  --success-foreground: oklch(1 0 0);

  --warning: oklch(75.8% 0.154 83.618);

  --warning-foreground: oklch(1 0 0);

  --info: oklch(63.9% 0.177 255.34);

  --info-foreground: oklch(1 0 0);

  --border: oklch(27.4% 0.006 286.033);

  --input: oklch(27.4% 0.006 286.033);

  --ring: #6444a8;

  --chart-1: oklch(0.488 0.243 264.376);

  --chart-2: oklch(0.696 0.17 162.48);

  --chart-3: oklch(0.769 0.188 70.08);

  --chart-4: oklch(0.627 0.265 303.9);

  --chart-5: oklch(0.645 0.246 16.439);

  --sidebar: oklch(0.205 0 0);

  --sidebar-foreground: oklch(0.985 0 0);

  --sidebar-primary: #6444a8;

  --sidebar-primary-foreground: oklch(0.985 0 0);

  --sidebar-accent: oklch(0.269 0 0);

  --sidebar-accent-foreground: oklch(0.985 0 0);

  --sidebar-border: oklch(1 0 0 / 10%);

  --sidebar-ring: oklch(0.556 0 0);
}