import { useState } from "react";
import { User<PERSON>he<PERSON>, Check, X, Clock, Search } from "lucide-react";
import Avatar from "../components/common/Avatar";
import StatusBadge from "../components/common/StatusBadge";
import Button from "../components/common/Button";
import Auth0AccountWizard from "@/components/modals/Auth0AccountWizard";
import { toast } from "sonner";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllRequests, updateRequestStatus } from "@/store/slices/accessRequestsSlice";
import { selectAllRoles } from "@/store/slices/rolesSlice";

export default function AccessRequests() {
  const requests = useAppSelector(selectAllRequests);
  const roles = useAppSelector(selectAllRoles);
  const dispatch = useAppDispatch();
  const [selectedRequests, setSelectedRequests] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showWizard, setShowWizard] = useState(false);

  const filteredRequests = requests.filter(
    (request) =>
      request.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.requestedRole.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectAll = (checked: boolean) => {
    const pendingRequests = filteredRequests.filter((r) => r.status === "pending");
    setSelectedRequests(checked ? pendingRequests.map((r) => r.id) : []);
  };

  const handleSelectRequest = (requestId: string, checked: boolean) => {
    setSelectedRequests((prev) => (checked ? [...prev, requestId] : prev.filter((id) => id !== requestId)));
  };

  const getRoleName = (roleId: string) => {
    const role = roles.find((r) => r.id === roleId);
    return role ? role.name : roleId;
  };

  const handleBulkApprove = () => {
    if (selectedRequests.length === 0) return;
    setShowWizard(true);
  };

  const handleSingleApprove = (requestId: string) => {
    setSelectedRequests([requestId]);
    setShowWizard(true);
  };

  const handleWizardComplete = () => {
    // Update request statuses to approved
    selectedRequests.forEach((requestId) => {
      dispatch(
        updateRequestStatus({
          requestId,
          status: "approved",
          reviewedBy: "Current User", // In a real app, get from auth state
        })
      );
    });
    setSelectedRequests([]);
    toast.success(`Successfully created Auth0 accounts for ${selectedRequests.length} user(s)`);
  };

  const handleDeny = (requestIds: string[]) => {
    requestIds.forEach((requestId) => {
      dispatch(
        updateRequestStatus({
          requestId,
          status: "denied",
          reviewedBy: "Current User", // In a real app, get from auth state
        })
      );
    });
    setSelectedRequests([]);
    toast.error(`Access denied for ${requestIds.length} user(s)`);
  };

  const selectedRequestObjects = requests.filter((r) => selectedRequests.includes(r.id));

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-wb-gray-900 flex items-center">
            <UserCheck className="w-7 h-7 mr-3 text-primary" />
            Access Requests
          </h1>
          <p className="text-wb-gray-600 mt-1">Review and manage user access requests for applications and permissions.</p>
        </div>
      </div>

      <div className="wb-card">
        <div className="p-6 border-b border-wb-gray-200">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-wb-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-wb-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
              />
            </div>
          </div>

          {selectedRequests.length > 0 && (
            <div className="mt-4 p-3 bg-primary/5 rounded-lg flex items-center justify-between">
              <span className="text-sm font-medium text-primary/90">
                {selectedRequests.length} request{selectedRequests.length > 1 ? "s" : ""} selected
              </span>
              <div className="flex space-x-2">
                <Button size="sm" variant="primary" onClick={handleBulkApprove}>
                  <Check className="w-4 h-4 mr-2" />
                  Create Auth0 Accounts
                </Button>
                <Button size="sm" variant="danger" onClick={() => handleDeny(selectedRequests)}>
                  <X className="w-4 h-4 mr-2" />
                  Bulk Deny
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-wb-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedRequests.length > 0 && selectedRequests.length === filteredRequests.filter((r) => r.status === "pending").length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="w-4 h-4 text-primary bg-white border-wb-gray-300 rounded focus:ring-primary/50"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider">User</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider">Requested Role</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider">Applications</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider">Request Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-wb-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-wb-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-wb-gray-200">
              {filteredRequests.map((request) => (
                <tr key={request.id} className="hover:bg-wb-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    {request.status === "pending" && (
                      <input
                        type="checkbox"
                        checked={selectedRequests.includes(request.id)}
                        onChange={(e) => handleSelectRequest(request.id, e.target.checked)}
                        className="w-4 h-4 text-primary bg-white border-wb-gray-300 rounded focus:ring-primary/50"
                      />
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Avatar name={request.userName} size="sm" className="mr-3" />
                      <div>
                        <div className="text-sm font-medium text-wb-gray-900">{request.userName}</div>
                        <div className="text-sm text-wb-gray-500">{request.userEmail}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-wb-gray-900">{getRoleName(request.requestedRole)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-wb-gray-900">
                      {request.requestedApplications.length} app{request.requestedApplications.length !== 1 ? "s" : ""}
                    </div>
                    <div className="text-xs text-wb-gray-500">
                      {request.requestedApplications.slice(0, 2).join(", ")}
                      {request.requestedApplications.length > 2 && ` +${request.requestedApplications.length - 2} more`}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-wb-gray-500">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1 text-wb-gray-400" />
                      {new Date(request.requestDate).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <StatusBadge status={request.status} />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right space-x-2">
                    {request.status === "pending" && (
                      <>
                        <Button size="sm" variant="primary" onClick={() => handleSingleApprove(request.id)}>
                          <Check className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="danger" onClick={() => handleDeny([request.id])}>
                          <X className="w-4 h-4" />
                        </Button>
                      </>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredRequests.length === 0 && (
          <div className="text-center py-12">
            <div className="text-wb-gray-400 mb-4">
              <UserCheck className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-wb-gray-900 mb-2">No requests found</h3>
            <p className="text-wb-gray-500">No access requests match your search criteria.</p>
          </div>
        )}
      </div>

      <Auth0AccountWizard
        open={showWizard}
        onClose={() => setShowWizard(false)}
        requests={selectedRequestObjects}
        onComplete={handleWizardComplete}
      />
    </div>
  );
}
