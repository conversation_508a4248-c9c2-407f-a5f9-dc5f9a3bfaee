import { Users, User<PERSON><PERSON><PERSON>, <PERSON>, Clock } from 'lucide-react';
import StatsCard from '../components/dashboard/StatsCard';
import PendingRequestsWidget from '../components/dashboard/PendingRequestsWidget';
import RecentActivity from '../components/dashboard/RecentActivity';
import { useAppSelector } from '@/store/hooks';
import { selectAllUsers } from '@/store/slices/usersSlice';
import { selectPendingRequests } from '@/store/slices/accessRequestsSlice';

export default function Dashboard() {
  const users = useAppSelector(selectAllUsers);
  const pendingRequestsList = useAppSelector(selectPendingRequests);
  
  const activeUsers = users.filter(user => user.status === 'active').length;
  const pendingRequests = pendingRequestsList.length;
  const totalUsers = users.length;
  const recentLogins = users.filter(user => 
    user.lastLogin && new Date(user.lastLogin).getTime() > Date.now() - 24 * 60 * 60 * 1000
  ).length;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">Welcome back, Sarah. Here's what's happening with your security administration.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Active Users"
          value={activeUsers}
          icon={<Users className="w-6 h-6" />}
          trend={{ value: 12, isPositive: true }}
        />
        <StatsCard
          title="Pending Requests"
          value={pendingRequests}
          icon={<Clock className="w-6 h-6" />}
          trend={{ value: 8, isPositive: false }}
        />
        <StatsCard
          title="Total Users"
          value={totalUsers}
          icon={<UserCheck className="w-6 h-6" />}
          trend={{ value: 5, isPositive: true }}
        />
        <StatsCard
          title="Recent Logins"
          value={recentLogins}
          icon={<Shield className="w-6 h-6" />}
          trend={{ value: 15, isPositive: true }}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <PendingRequestsWidget />
        </div>
        <div className="lg:col-span-2">
          <RecentActivity />
        </div>
      </div>
    </div>
  );
}