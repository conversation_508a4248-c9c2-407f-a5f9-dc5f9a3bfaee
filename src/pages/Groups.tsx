import { useState } from "react";
import { Users, Plus, Search, Settings, Shield, UserPlus, Key, AppWindow } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllGroups, addGroup, updateGroup, deleteGroup } from "@/store/slices/groupsSlice";
import { selectAllUsers } from "@/store/slices/usersSlice";
import { selectAllAssignments } from "@/store/slices/roleApplicationAssignmentsSlice";
import GroupCreationWizard from "@/components/modals/GroupCreationWizard";
import type { Group } from "@/types";

export default function Groups() {
  const groups = useAppSelector(selectAllGroups);
  const users = useAppSelector(selectAllUsers);
  const assignments = useAppSelector(selectAllAssignments);
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [isWizardOpen, setIsWizardOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);

  const filteredGroups = groups.filter(
    (group) => group.name.toLowerCase().includes(searchTerm.toLowerCase()) || group.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getMemberDetails = (memberId: string) => {
    return users.find((user) => user.id === memberId);
  };

  const getGroupAssignments = (group: Group) => {
    const assignmentIds = group.assignmentIds || [];
    return assignments.filter((a) => assignmentIds.includes(a.id));
  };

  const getAssignmentSummary = (group: Group) => {
    const groupAssignments = getGroupAssignments(group);
    const apps = new Set(groupAssignments.map((a) => a.applicationName));
    const roles = new Set(groupAssignments.map((a) => a.roleName));
    
    let totalPermissions = 0;
    groupAssignments.forEach((assignment) => {
      Object.values(assignment.permissions).forEach((perms) => {
        totalPermissions += perms.length;
      });
    });
    
    return {
      assignmentCount: groupAssignments.length,
      appCount: apps.size,
      roleCount: roles.size,
      permissionCount: totalPermissions,
    };
  };

  const handleCreateGroup = () => {
    setEditingGroup(null);
    setIsWizardOpen(true);
  };

  const handleEditGroup = (group: Group) => {
    setEditingGroup(group);
    setIsWizardOpen(true);
  };

  const handleGroupComplete = (group: Partial<Group>) => {
    if (editingGroup) {
      dispatch(updateGroup({ ...editingGroup, ...group } as Group));
    } else {
      dispatch(addGroup(group as Group));
    }
    setIsWizardOpen(false);
    setEditingGroup(null);
  };

  const handleDeleteGroup = (groupId: string) => {
    dispatch(deleteGroup(groupId));
    setSelectedGroup(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center">
            <Users className="w-7 h-7 mr-3 text-primary" />
            Groups Management
          </h1>
          <p className="text-slate-600 mt-1">Manage user groups and their permissions across the organization.</p>
        </div>
        <Button onClick={handleCreateGroup}>
          <Plus className="w-4 h-4 mr-2" />
          Create Group
        </Button>
      </div>

      <div className="flex gap-6">
        <div className="flex-1 space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
            <Input type="text" placeholder="Search groups..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredGroups.map((group) => (
              <Card
                key={group.id}
                className={`cursor-pointer transition-all hover:shadow-lg ${selectedGroup?.id === group.id ? "ring-2 ring-primary" : ""}`}
                onClick={() => setSelectedGroup(group)}
              >
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{group.name}</CardTitle>
                        <CardDescription className="text-sm mt-1">{group.description}</CardDescription>
                      </div>
                    </div>
                    <Badge variant="secondary">{group.memberCount} members</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-slate-500">Assignments</span>
                      <span className="font-medium">{getAssignmentSummary(group).assignmentCount}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-slate-500">Total Permissions</span>
                      <span className="font-medium">{getAssignmentSummary(group).permissionCount}</span>
                    </div>
                    {getGroupAssignments(group).length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {getGroupAssignments(group).slice(0, 2).map((assignment) => (
                          <Badge key={assignment.id} variant="secondary" className="text-xs">
                            {assignment.roleName} • {assignment.applicationName}
                          </Badge>
                        ))}
                        {getGroupAssignments(group).length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{getGroupAssignments(group).length - 2} more
                          </Badge>
                        )}
                      </div>
                    )}
                    {group.members.length > 0 && (
                      <div className="flex -space-x-2">
                        {group.members.slice(0, 5).map((memberId) => {
                          const member = getMemberDetails(memberId);
                          return member ? (
                            <Avatar key={memberId} className="w-8 h-8 border-2 border-white">
                              <AvatarFallback className="text-xs bg-primary/10 text-primary">{getInitials(member.name)}</AvatarFallback>
                            </Avatar>
                          ) : null;
                        })}
                        {group.members.length > 5 && (
                          <div className="w-8 h-8 rounded-full bg-slate-100 border-2 border-white flex items-center justify-center">
                            <span className="text-xs text-slate-600">+{group.members.length - 5}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {selectedGroup && (
          <Card className="w-96">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Group Details</CardTitle>
                <Button variant="ghost" size="icon">
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-sm text-slate-700 mb-2">Description</h3>
                <p className="text-sm text-slate-600">{selectedGroup.description}</p>
              </div>

              <div>
                <h3 className="font-medium text-sm text-slate-700 mb-2">
                  Role-Application Assignments ({getGroupAssignments(selectedGroup).length})
                </h3>
                {getGroupAssignments(selectedGroup).length > 0 ? (
                  <div className="space-y-2">
                    {getGroupAssignments(selectedGroup).map((assignment) => {
                      const permissionCount = Object.values(assignment.permissions).reduce(
                        (acc, perms) => acc + perms.length,
                        0
                      );
                      const pageCount = Object.keys(assignment.permissions).length;
                      
                      return (
                        <div key={assignment.id} className="p-3 bg-slate-50 rounded-lg">
                          <div className="flex items-start justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <Key className="w-4 h-4 text-primary" />
                                <span className="text-sm font-medium">{assignment.roleName}</span>
                              </div>
                              <div className="flex items-center gap-2 ml-6">
                                <AppWindow className="w-3 h-3 text-slate-500" />
                                <span className="text-xs text-slate-600">{assignment.applicationName}</span>
                              </div>
                            </div>
                            <Badge variant="secondary" className="text-xs">
                              {pageCount} pages • {permissionCount} permissions
                            </Badge>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <p className="text-sm text-slate-500">No assignments configured</p>
                )}
              </div>

              <div>
                <h3 className="font-medium text-sm text-slate-700 mb-2">Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-500">Total Applications</span>
                    <span className="font-medium">{getAssignmentSummary(selectedGroup).appCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-500">Total Roles</span>
                    <span className="font-medium">{getAssignmentSummary(selectedGroup).roleCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-500">Total Permissions</span>
                    <span className="font-medium">{getAssignmentSummary(selectedGroup).permissionCount}</span>
                  </div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-sm text-slate-700">Members ({selectedGroup.memberCount})</h3>
                  <Button variant="ghost" size="sm">
                    <UserPlus className="w-4 h-4 mr-1" />
                    Add
                  </Button>
                </div>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {selectedGroup.members.length > 0 ? (
                    selectedGroup.members.map((memberId) => {
                      const member = getMemberDetails(memberId);
                      return member ? (
                        <div key={memberId} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-slate-50">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback className="text-xs bg-primary/10 text-primary">{getInitials(member.name)}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{member.name}</p>
                            <p className="text-xs text-slate-500">{member.email}</p>
                          </div>
                        </div>
                      ) : null;
                    })
                  ) : (
                    <p className="text-sm text-slate-500 text-center py-4">No members assigned</p>
                  )}
                </div>
              </div>

              <div className="pt-4 space-y-2">
                <Button className="w-full" variant="default" onClick={() => handleEditGroup(selectedGroup)}>
                  Edit Group
                </Button>
                <Button className="w-full" variant="destructive" onClick={() => handleDeleteGroup(selectedGroup.id)}>
                  Delete Group
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <GroupCreationWizard
        open={isWizardOpen}
        onClose={() => {
          setIsWizardOpen(false);
          setEditingGroup(null);
        }}
        editingGroup={editingGroup}
        onComplete={handleGroupComplete}
      />
    </div>
  );
}
