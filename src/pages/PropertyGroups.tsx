import { useState } from "react";
import { Building2, Plus, Search, Map, Trash2, Edit2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllPropertyGroups, deletePropertyGroup } from "@/store/slices/propertyGroupsSlice";
import { selectAllProperties } from "@/store/slices/propertiesSlice";
import PropertyGroupCreationWizard from "@/components/modals/PropertyGroupCreationWizard";
import type { PropertyGroup } from "@/types";

export default function PropertyGroups() {
  const propertyGroups = useAppSelector(selectAllPropertyGroups);
  const properties = useAppSelector(selectAllProperties);
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedGroup, setSelectedGroup] = useState<PropertyGroup | null>(null);
  const [isWizardOpen, setIsWizardOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<PropertyGroup | null>(null);

  const filteredGroups = propertyGroups.filter(
    (group) => group.name.toLowerCase().includes(searchTerm.toLowerCase()) || group.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getPropertyDetails = (propertyId: string) => {
    return properties.find((property) => property.id === propertyId);
  };

  const handleCreateGroup = () => {
    setEditingGroup(null);
    setIsWizardOpen(true);
  };

  const handleEditGroup = (group: PropertyGroup) => {
    setEditingGroup(group);
    setIsWizardOpen(true);
  };

  const handleDeleteGroup = (groupId: string) => {
    dispatch(deletePropertyGroup(groupId));
    setSelectedGroup(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center">
            <Building2 className="w-7 h-7 mr-3 text-primary" />
            Property Groups
          </h1>
          <p className="text-slate-600 mt-1">Organize properties into logical groups for easier management.</p>
        </div>
        <Button onClick={handleCreateGroup}>
          <Plus className="w-4 h-4 mr-2" />
          Create Property Group
        </Button>
      </div>

      <div className="flex gap-6">
        <div className="flex-1 space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
            <Input
              type="text"
              placeholder="Search property groups..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredGroups.map((group) => (
              <Card
                key={group.id}
                className={`cursor-pointer transition-all hover:shadow-lg ${selectedGroup?.id === group.id ? "ring-2 ring-primary" : ""}`}
                onClick={() => setSelectedGroup(group)}
              >
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Building2 className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{group.name}</CardTitle>
                        <CardDescription className="mt-1">{group.description}</CardDescription>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center text-slate-600">
                      <Map className="w-4 h-4 mr-1" />
                      {group.properties.length} properties
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditGroup(group);
                        }}
                      >
                        <Edit2 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteGroup(group.id);
                        }}
                      >
                        <Trash2 className="w-4 h-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {selectedGroup && (
          <div className="w-96">
            <Card className="sticky top-0">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Building2 className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle>{selectedGroup.name}</CardTitle>
                      <CardDescription>{selectedGroup.description}</CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-2">Properties ({selectedGroup.properties.length})</h4>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {selectedGroup.properties.map((propertyId) => {
                      const property = getPropertyDetails(propertyId);
                      return property ? (
                        <div key={property.id} className="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                          <div>
                            <p className="font-medium text-sm">{property.name}</p>
                            <p className="text-xs text-slate-600">{property.type}</p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {property.region}
                          </Badge>
                        </div>
                      ) : null;
                    })}
                  </div>
                </div>

                <div className="pt-4 border-t space-y-2">
                  <Button className="w-full" variant="outline" onClick={() => handleEditGroup(selectedGroup)}>
                    <Edit2 className="w-4 h-4 mr-2" />
                    Edit Group
                  </Button>
                  <Button className="w-full" variant="outline" onClick={() => handleDeleteGroup(selectedGroup.id)}>
                    <Trash2 className="w-4 h-4 mr-2 text-red-500" />
                    Delete Group
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      <PropertyGroupCreationWizard
        open={isWizardOpen}
        onClose={() => {
          setIsWizardOpen(false);
          setEditingGroup(null);
        }}
        editingGroup={editingGroup}
      />
    </div>
  );
}
