import { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { MultiSelect } from "@/components/ui/multi-select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AppWindow, Search, Check, ChevronLeft, ChevronRight, Shield, Key, Plus, Trash2, Settings } from "lucide-react";
import { cn } from "@/lib/utils";
import { applications } from "@/data/applications";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllRoles } from "@/store/slices/rolesSlice";
import {
  selectAllAssignments,
  addAssignment,
  updateAssignment,
  deleteAssignment,
  type RoleApplicationAssignment
} from "@/store/slices/roleApplicationAssignmentsSlice";
import { selectAllApplicationPages } from "@/store/slices/applicationPagesSlice";
import { toast } from "sonner";
import { CRUD_OPERATIONS, CRUD_LABELS, CRUD_COLORS } from "@/constants/permissions";

interface PagePermissions {
  pageId: string;
  pageName: string;
  permissions: {
    [roleId: string]: string[];
  };
}

type Step = "selectApplication" | "configurePermissions";
type ViewMode = "dashboard" | "create";

// Custom Permission MultiSelect Component with color-coded badges
const PermissionMultiSelect = ({
  permissions,
  onChange,
  className
}: {
  permissions: string[];
  onChange: (perms: string[]) => void;
  className?: string;
}) => {
  const options = CRUD_OPERATIONS.map(op => ({
    value: op,
    label: CRUD_LABELS[op]
  }));

  // Create a more descriptive placeholder showing first 2 permissions
  const getPlaceholder = () => {
    if (permissions.length === 0) return "Select...";
    if (permissions.length === 1) {
      const label = CRUD_LABELS[permissions[0] as keyof typeof CRUD_LABELS];
      return label || permissions[0];
    }
    if (permissions.length === 2) {
      const labels = permissions.map(p => {
        const label = CRUD_LABELS[p as keyof typeof CRUD_LABELS];
        return label ? label.charAt(0) : p.charAt(0);
      });
      return labels.join(", ");
    }
    // For 3+ items, show first item and count
    const firstLabel = CRUD_LABELS[permissions[0] as keyof typeof CRUD_LABELS] || permissions[0];
    return `${firstLabel}, +${permissions.length - 1}`;
  };

  return (
    <MultiSelect
      options={options}
      selected={permissions}
      onChange={onChange}
      placeholder={getPlaceholder()}
      searchPlaceholder="Search..."
      emptyText="No permissions found."
      className={cn("w-full", className)}
    />
  );
};


export default function RoleApplicationAssignment() {
  const dispatch = useAppDispatch();
  const roles = useAppSelector(selectAllRoles);
  const assignments = useAppSelector(selectAllAssignments);
  
  // State
  const [view, setView] = useState<ViewMode>("dashboard");
  const [currentStep, setCurrentStep] = useState<Step>("selectApplication");
  const [selectedApplication, setSelectedApplication] = useState<string | null>(null);
  const [appSearch, setAppSearch] = useState("");
  const [assignmentSearch, setAssignmentSearch] = useState("");
  const [pagePermissions, setPagePermissions] = useState<PagePermissions[]>([]);
  const [showOnlyConfigured, setShowOnlyConfigured] = useState(false);
  const [editingApplication, setEditingApplication] = useState<string | null>(null);

  // Filtered assignments
  const filteredAssignments = useMemo(() => {
    return assignments.filter(
      (assignment) =>
        assignment.roleName.toLowerCase().includes(assignmentSearch.toLowerCase()) ||
        assignment.applicationName.toLowerCase().includes(assignmentSearch.toLowerCase())
    );
  }, [assignments, assignmentSearch]);

  // Filtered applications
  const filteredApplications = useMemo(() => {
    return applications.filter(
      (app) =>
        app.name.toLowerCase().includes(appSearch.toLowerCase()) ||
        app.description.toLowerCase().includes(appSearch.toLowerCase())
    );
  }, [appSearch]);

  // Get selected application object
  const selectedAppObj = useMemo(() => {
    return applications.find((app) => app.id === selectedApplication);
  }, [selectedApplication]);

  // Get permission count for an assignment
  const getPermissionCount = (assignment: RoleApplicationAssignment) => {
    let count = 0;
    Object.values(assignment.permissions).forEach((perms) => {
      count += perms.length;
    });
    return count;
  };

  // Get all assignments for a specific application
  const getApplicationAssignments = (applicationId: string) => {
    return assignments.filter((a) => a.applicationId === applicationId);
  };


  // Get all application pages from Redux store
  const allApplicationPages = useAppSelector(selectAllApplicationPages);

  // Initialize permissions for all roles when selecting an application
  const initializePermissions = (appId?: string) => {
    const app = appId || selectedApplication;

    if (app) {
      const pages = allApplicationPages[app] || [];
      const existingAssignments = getApplicationAssignments(app);

      const initialPermissions: PagePermissions[] = pages.map((page) => {
        const permissions: { [roleId: string]: string[] } = {};

        // Initialize empty permissions for all roles
        roles.forEach((role) => {
          permissions[role.id] = [];
        });

        // Load existing permissions from assignments
        existingAssignments.forEach((assignment) => {
          if (assignment.permissions[page.id]) {
            permissions[assignment.roleId] = assignment.permissions[page.id];
          }
        });

        return {
          pageId: page.id,
          pageName: page.name,
          permissions,
        };
      });

      setPagePermissions(initialPermissions);
    }
  };

  // Handle starting new assignment creation
  const handleCreateNew = () => {
    setView("create");
    setCurrentStep("selectApplication");
    setSelectedApplication(null);
    setPagePermissions([]);
    setEditingApplication(null);
  };

  // Handle configuring permissions for an application
  const handleConfigureApplication = (applicationId: string) => {
    setSelectedApplication(applicationId);
    setEditingApplication(applicationId);
    initializePermissions(applicationId);
    setView("create");
    setCurrentStep("configurePermissions");
  };

  // Handle step navigation
  const handleApplicationSelect = (appId: string) => {
    setSelectedApplication(appId);
    initializePermissions(appId);
    setCurrentStep("configurePermissions");
  };

  const handleBack = () => {
    if (currentStep === "configurePermissions") {
      setCurrentStep("selectApplication");
      setPagePermissions([]);
      setSelectedApplication(null);
    }
  };

  const handleBackToDashboard = () => {
    setView("dashboard");
    setCurrentStep("selectApplication");
    setSelectedApplication(null);
    setPagePermissions([]);
    setEditingApplication(null);
  };

  // Handle assignment deletion
  const handleDeleteAssignment = (assignmentId: string) => {
    dispatch(deleteAssignment(assignmentId));
    toast.success("Assignment deleted successfully");
  };


  // Get permission summary
  const getPermissionSummary = () => {
    let totalPermissions = 0;
    let configuredPages = 0;
    const byOperation = { create: 0, read: 0, update: 0, delete: 0, execute: 0 };

    pagePermissions.forEach((page) => {
      let pageHasPermissions = false;
      Object.values(page.permissions).forEach((permissions) => {
        if (permissions.length > 0) {
          pageHasPermissions = true;
          permissions.forEach((perm) => {
            if (perm in byOperation) {
              byOperation[perm as keyof typeof byOperation]++;
              totalPermissions++;
            }
          });
        }
      });
      if (pageHasPermissions) configuredPages++;
    });

    return { totalPermissions, configuredPages, byOperation };
  };

  const summary = getPermissionSummary();
  const filteredPages = showOnlyConfigured
    ? pagePermissions.filter((page) => Object.values(page.permissions).some((perms) => perms.length > 0))
    : pagePermissions;

  // Save handler for multiple role assignments
  const handleSave = () => {
    if (selectedApplication && selectedAppObj) {
      const existingAssignments = getApplicationAssignments(selectedApplication);
      let createdCount = 0;
      let updatedCount = 0;
      let deletedCount = 0;

      // Process each role
      roles.forEach((role) => {
        // Prepare permissions for this role
        const rolePermissions: { [pageId: string]: string[] } = {};
        let hasAnyPermissions = false;

        pagePermissions.forEach((page) => {
          if (page.permissions[role.id] && page.permissions[role.id].length > 0) {
            rolePermissions[page.pageId] = page.permissions[role.id];
            hasAnyPermissions = true;
          }
        });

        // Find existing assignment for this role
        const existingAssignment = existingAssignments.find(a => a.roleId === role.id);

        if (hasAnyPermissions) {
          if (existingAssignment) {
            // Update existing assignment
            const updatedAssignment: RoleApplicationAssignment = {
              ...existingAssignment,
              permissions: rolePermissions,
              modifiedAt: new Date().toISOString().split('T')[0],
            };
            dispatch(updateAssignment(updatedAssignment));
            updatedCount++;
          } else {
            // Create new assignment
            const newAssignment: RoleApplicationAssignment = {
              id: `assign_${Date.now()}_${role.id}`,
              roleId: role.id,
              roleName: role.name,
              applicationId: selectedApplication,
              applicationName: selectedAppObj.name,
              permissions: rolePermissions,
              createdAt: new Date().toISOString().split('T')[0],
              modifiedAt: new Date().toISOString().split('T')[0],
            };
            dispatch(addAssignment(newAssignment));
            createdCount++;
          }
        } else if (existingAssignment) {
          // Delete assignment if no permissions remain
          dispatch(deleteAssignment(existingAssignment.id));
          deletedCount++;
        }
      });

      // Show summary toast
      const messages = [];
      if (createdCount > 0) messages.push(`${createdCount} created`);
      if (updatedCount > 0) messages.push(`${updatedCount} updated`);
      if (deletedCount > 0) messages.push(`${deletedCount} removed`);

      if (messages.length > 0) {
        toast.success(`Role assignments: ${messages.join(', ')}`);
      } else {
        toast.info("No changes made");
      }

      handleBackToDashboard();
    }
  };

  // Step indicator component
  const StepIndicator = () => (
    <div className="flex items-center justify-center space-x-4 mb-6">
      <div className={cn("flex items-center", currentStep === "selectApplication" ? "text-primary" : "text-slate-400")}>
        <div className={cn(
          "w-8 h-8 rounded-full flex items-center justify-center font-semibold",
          currentStep === "selectApplication" ? "bg-primary text-white" : "bg-slate-200"
        )}>
          1
        </div>
        <span className="ml-2 text-sm font-medium">Select Application</span>
      </div>
      <ChevronRight className="w-5 h-5 text-slate-400" />
      <div className={cn("flex items-center", currentStep === "configurePermissions" ? "text-primary" : "text-slate-400")}>
        <div className={cn(
          "w-8 h-8 rounded-full flex items-center justify-center font-semibold",
          currentStep === "configurePermissions" ? "bg-primary text-white" : "bg-slate-200"
        )}>
          2
        </div>
        <span className="ml-2 text-sm font-medium">Configure Permissions</span>
      </div>
    </div>
  );

  // Dashboard View
  if (view === "dashboard") {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-slate-900 flex items-center">
              <Shield className="w-7 h-7 mr-3 text-primary" />
              Role Application Assignment
            </h1>
            <p className="text-slate-600 mt-1">Manage role permissions for applications</p>
          </div>
          <Button onClick={handleCreateNew}>
            <Plus className="w-4 h-4 mr-2" />
            Create New Assignment
          </Button>
        </div>

        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
          <Input
            type="text"
            placeholder="Search assignments..."
            value={assignmentSearch}
            onChange={(e) => setAssignmentSearch(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="bg-white rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Role Name</TableHead>
                <TableHead className="w-[250px]">Application</TableHead>
                <TableHead className="w-[140px]">Pages Configured</TableHead>
                <TableHead className="w-[150px]">Total Permissions</TableHead>
                <TableHead className="w-[120px]">Last Modified</TableHead>
                <TableHead className="w-[180px] text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAssignments.map((assignment) => {
                const permissionCount = getPermissionCount(assignment);
                const pageCount = Object.keys(assignment.permissions).length;

                return (
                  <TableRow key={assignment.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <Key className="w-4 h-4 text-primary" />
                        <span>{assignment.roleName}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <AppWindow className="w-4 h-4 text-primary" />
                        <span className="text-slate-600">{assignment.applicationName}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="text-xs">
                        {pageCount} pages
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="text-xs">
                        {permissionCount} permissions
                      </Badge>
                    </TableCell>
                    <TableCell className="text-slate-600">
                      {assignment.modifiedAt}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleConfigureApplication(assignment.applicationId)}
                        >
                          <Settings className="w-3 h-3 mr-1" />
                          Configure
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteAssignment(assignment.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
              {filteredAssignments.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-12">
                    <Shield className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-900 mb-2">No Assignments Found</h3>
                    <p className="text-slate-600 mb-6">
                      {assignmentSearch
                        ? "No assignments match your search criteria"
                        : "Get started by creating your first role-application assignment"}
                    </p>
                    {!assignmentSearch && (
                      <Button onClick={handleCreateNew}>
                        <Plus className="w-4 h-4 mr-2" />
                        Create First Assignment
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  // Create View (2-step flow)
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b flex-shrink-0">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 md:space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={currentStep === "selectApplication" ? handleBackToDashboard : handleBack}
                className="text-slate-600 hover:text-slate-900"
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                <span className="hidden sm:inline">{currentStep === "selectApplication" ? "Back to Assignments" : "Back"}</span>
                <span className="sm:hidden">Back</span>
              </Button>
              <div className="flex items-center space-x-2">
                <div className="p-1.5 bg-primary/10 rounded-lg">
                  <Shield className="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-slate-900">
                    Configure Role Permissions
                  </h1>
                  <p className="text-xs text-slate-600 hidden md:block">Manage permissions for all roles in an application</p>
                </div>
              </div>
            </div>

            {currentStep === "configurePermissions" && (
              <div className="flex items-center space-x-2">
                <div className="text-sm text-slate-600 hidden md:block">
                  {summary.totalPermissions} total permission{summary.totalPermissions !== 1 ? 's' : ''} configured
                </div>
                <Button onClick={handleSave} size="sm">
                  <Check className="w-4 h-4 mr-1" />
                  Save All Changes
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-auto p-6">
        <StepIndicator />

        {/* Step 1: Select Application */}
        {currentStep === "selectApplication" && (
          <div className="max-w-6xl mx-auto">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-slate-900 mb-2">Select an Application</h2>
              <p className="text-slate-600">
                Choose the application to configure role permissions for all roles
              </p>
            </div>

            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search applications..."
                value={appSearch}
                onChange={(e) => setAppSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredApplications.map((app) => {
                const existingAssignments = getApplicationAssignments(app.id);
                const hasAssignments = existingAssignments.length > 0;

                return (
                  <Card
                    key={app.id}
                    className={cn(
                      "cursor-pointer hover:shadow-lg transition-all",
                      editingApplication === app.id && "border-primary ring-2 ring-primary/20"
                    )}
                    onClick={() => handleApplicationSelect(app.id)}
                  >
                    <CardHeader>
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <AppWindow className="w-5 h-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <CardTitle className="text-lg">{app.name}</CardTitle>
                              <p className="text-sm text-slate-600 mt-1">{app.description}</p>
                            </div>
                            {hasAssignments && (
                              <Badge variant="secondary" className="ml-2">
                                {existingAssignments.length} roles
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-slate-500">
                          {hasAssignments
                            ? `Click to manage ${existingAssignments.length} role configuration(s)`
                            : "Click to configure roles"
                          }
                        </span>
                        <ChevronRight className="w-5 h-5 text-slate-400" />
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}

        {/* Step 2: Configure Permissions */}
        {currentStep === "configurePermissions" && selectedAppObj && (
          <div className="w-full max-w-[calc(100vw-3rem)]">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-slate-900 mb-2">Configure Permissions</h2>
              <p className="text-slate-600">
                Set permissions for all roles in <strong>{selectedAppObj.name}</strong>
              </p>
            </div>

            {/* Summary Stats */}
            {summary.totalPermissions > 0 && (
              <div className="mb-6 flex flex-wrap items-center gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <AppWindow className="w-4 h-4 text-slate-400" />
                  <span className="text-slate-600">Application: {selectedAppObj?.name}</span>
                </div>
                {CRUD_OPERATIONS.map((op) => (
                  <div key={op} className="flex items-center space-x-2">
                    <div className={cn("w-3 h-3 rounded", CRUD_COLORS[op])} />
                    <span className="text-slate-600">
                      {summary.byOperation[op]} {CRUD_LABELS[op]}
                    </span>
                  </div>
                ))}
              </div>
            )}

            {/* Content Header */}
            <div className="p-4 border rounded-lg bg-white mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-slate-900">Page Permissions</h3>
                  <p className="text-sm text-slate-600 mt-1">Configure Create, Read, Update, Delete & Execute permissions for each page</p>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-configured"
                    checked={showOnlyConfigured}
                    onCheckedChange={setShowOnlyConfigured}
                  />
                  <Label htmlFor="show-configured" className="text-sm">
                    Show only configured
                  </Label>
                </div>
              </div>
            </div>

            {/* Permissions Table */}
            <div className="relative bg-white rounded-lg border">
              <div className="overflow-x-auto overflow-y-auto max-h-[600px]">
                <table className="w-full relative">
                  <thead className="bg-slate-50 border-b sticky top-0 z-20">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider sticky left-0 bg-slate-50 z-30 min-w-[120px] border-r border-slate-200">
                        Pages
                      </th>
                      {roles.map((role) => {
                        // Count permissions for this role
                        const rolePermissionCount = pagePermissions.reduce((count, page) => {
                          return count + (page.permissions[role.id]?.length || 0);
                        }, 0);

                        return (
                          <th
                            key={role.id}
                            className="px-2 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider min-w-[140px]"
                          >
                            <div>
                              <div>{role.name}</div>
                              {rolePermissionCount > 0 && (
                                <div className="text-xs font-normal text-slate-500 mt-1">
                                  {rolePermissionCount} permission{rolePermissionCount !== 1 ? 's' : ''}
                                </div>
                              )}
                            </div>
                          </th>
                        );
                      })}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-slate-200">
                    {filteredPages.map((page) => {
                      return (
                        <tr key={page.pageId} className="hover:bg-slate-50">
                          <td className="px-4 py-3 text-sm font-medium text-slate-900 sticky left-0 bg-white z-10 border-r border-slate-200">
                            {page.pageName}
                          </td>
                          {roles.map((role) => {
                            const rolePermissions = page.permissions[role.id] || [];
                            return (
                              <td key={role.id} className="px-2 py-3">
                                <div className="w-full max-w-[130px]">
                                  <PermissionMultiSelect
                                    permissions={rolePermissions}
                                    onChange={(newPermissions) => {
                                      // Update permissions for this role and page
                                      setPagePermissions((prev) =>
                                        prev.map((p) => {
                                          if (p.pageId === page.pageId) {
                                            return {
                                              ...p,
                                              permissions: {
                                                ...p.permissions,
                                                [role.id]: newPermissions,
                                              },
                                            };
                                          }
                                          return p;
                                        })
                                      );
                                    }}
                                  />
                                </div>
                              </td>
                            );
                          })}
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {filteredPages.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-slate-600">No pages to configure</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}