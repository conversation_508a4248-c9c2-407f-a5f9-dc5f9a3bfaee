import { useState, useEffect } from "react";
import { Shield, Plus, Search, Edit2, Trash2, Save, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllRoles, addRole, updateRole, deleteRole, setRoles } from "@/store/slices/rolesSlice";
import { roles as initialRoles } from "@/data/roles";
import type { Role } from "@/types";
import { toast } from "sonner";

export default function RoleManagement() {
  const dispatch = useAppDispatch();
  const rolesFromStore = useAppSelector(selectAllRoles);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [formData, setFormData] = useState({ name: "", description: "" });
  const [deleteConfirmRole, setDeleteConfirmRole] = useState<Role | null>(null);

  useEffect(() => {
    if (rolesFromStore.length === 0 || rolesFromStore.length < initialRoles.length) {
      dispatch(setRoles(initialRoles));
    }
  }, [dispatch, rolesFromStore.length]);

  const roles = rolesFromStore.length > 0 ? rolesFromStore : initialRoles;

  const filteredRoles = roles.filter(
    (role) =>
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEdit = (role: Role) => {
    setEditingRole(role);
    setFormData({ name: role.name, description: role.description });
  };

  const handleSaveEdit = () => {
    if (editingRole && formData.name && formData.description) {
      dispatch(
        updateRole({
          ...editingRole,
          name: formData.name,
          description: formData.description,
        })
      );
      toast.success("Role updated successfully");
      setEditingRole(null);
      setFormData({ name: "", description: "" });
    }
  };

  const handleCancelEdit = () => {
    setEditingRole(null);
    setFormData({ name: "", description: "" });
  };

  const handleCreate = () => {
    if (formData.name && formData.description) {
      const newRole: Role = {
        id: `role_${Date.now()}`,
        name: formData.name,
        description: formData.description,
        permissions: [],
      };
      dispatch(addRole(newRole));
      toast.success("Role created successfully");
      setIsCreateDialogOpen(false);
      setFormData({ name: "", description: "" });
    }
  };

  const handleDelete = (role: Role) => {
    setDeleteConfirmRole(role);
  };

  const confirmDelete = () => {
    if (deleteConfirmRole) {
      dispatch(deleteRole(deleteConfirmRole.id));
      toast.success(`Role "${deleteConfirmRole.name}" deleted successfully`);
      setDeleteConfirmRole(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center">
            <Shield className="w-7 h-7 mr-3 text-primary" />
            Role Management
          </h1>
          <p className="text-slate-600 mt-1">Create and manage roles with names and descriptions</p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Role
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
        <Input
          type="text"
          placeholder="Search roles..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Role Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="w-[150px]">Permissions</TableHead>
              <TableHead className="w-[120px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRoles.map((role) => (
              <TableRow key={role.id}>
                <TableCell className="font-medium">
                  {editingRole?.id === role.id ? (
                    <Input
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full"
                    />
                  ) : (
                    role.name
                  )}
                </TableCell>
                <TableCell>
                  {editingRole?.id === role.id ? (
                    <Input
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="w-full"
                    />
                  ) : (
                    <span className="text-slate-600">{role.description}</span>
                  )}
                </TableCell>
                <TableCell>
                  {role.permissions.length > 0 ? (
                    <Badge variant="secondary" className="text-xs">
                      {role.permissions.length} permissions
                    </Badge>
                  ) : (
                    <span className="text-slate-400 text-xs">No permissions</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end space-x-2">
                    {editingRole?.id === role.id ? (
                      <>
                        <Button size="sm" variant="default" onClick={handleSaveEdit}>
                          <Save className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                          <X className="w-4 h-4" />
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button size="sm" variant="outline" onClick={() => handleEdit(role)}>
                          <Edit2 className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDelete(role)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {filteredRoles.length === 0 && (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-12">
                  <Shield className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 mb-2">No Roles Found</h3>
                  <p className="text-slate-600 mb-6">
                    {searchTerm
                      ? "No roles match your search criteria"
                      : "Get started by creating your first role"}
                  </p>
                  {!searchTerm && (
                    <Button onClick={() => setIsCreateDialogOpen(true)}>
                      <Plus className="w-4 h-4 mr-2" />
                      Create First Role
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Create Role Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Role</DialogTitle>
            <DialogDescription>Add a new role with a name and description</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="new-name">Role Name</Label>
              <Input
                id="new-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter role name"
              />
            </div>
            <div>
              <Label htmlFor="new-description">Description</Label>
              <Input
                id="new-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter role description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreate} disabled={!formData.name || !formData.description}>
              Create Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!deleteConfirmRole} onOpenChange={() => setDeleteConfirmRole(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Role</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the role "{deleteConfirmRole?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteConfirmRole(null)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}