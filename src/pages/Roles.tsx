import { useState } from "react";
import { Shield, Plus, Search, Settings, Users, Key, Edit, Trash2, Copy } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectAllRoles, addRole, updateRole, deleteRole as deleteRoleAction } from "@/store/slices/rolesSlice";
import { selectAllUsers } from "@/store/slices/usersSlice";
import type { Role } from "@/types";
import EnhancedRoleCreationWizard from "@/components/modals/EnhancedRoleCreationWizard";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

export default function Roles() {
  const roles = useAppSelector(selectAllRoles);
  const users = useAppSelector(selectAllUsers);
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isCreationWizardOpen, setIsCreationWizardOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);

  const filteredRoles = roles.filter(
    (role) => role.name.toLowerCase().includes(searchTerm.toLowerCase()) || role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getUsersWithRole = (roleId: string) => {
    return users.filter((user) => user.currentRole === roleId);
  };

  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    setIsCreationWizardOpen(true);
  };

  const handleCloneRole = (role: Role) => {
    const clonedRole = { ...role, id: "", name: `${role.name} (Copy)` };
    setEditingRole(clonedRole);
    setIsCreationWizardOpen(true);
  };

  const handleDeleteRole = (role: Role) => {
    if (confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
      dispatch(deleteRoleAction(role.id));
      if (selectedRole?.id === role.id) {
        setSelectedRole(null);
      }
    }
  };

  const handleRoleComplete = (role: Partial<Role>) => {
    if (editingRole && editingRole.id) {
      dispatch(updateRole({ ...editingRole, ...role } as Role));
    } else {
      const newRole: Role = {
        ...role,
        id: `role_${Date.now()}`,
      } as Role;
      dispatch(addRole(newRole));
    }
    setIsCreationWizardOpen(false);
    setEditingRole(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center">
            <Shield className="w-7 h-7 mr-3 text-primary" />
            Role Application Assignment
          </h1>
          <p className="text-slate-600 mt-1">Manage roles and their permissions for applications and resources.</p>
        </div>
        <Button
          onClick={() => {
            setEditingRole(null);
            setIsCreationWizardOpen(true);
          }}
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Role
        </Button>
      </div>

      <div className="flex gap-6">
        <div className="flex-1 space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
            <Input type="text" placeholder="Search roles..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredRoles.map((role) => {
              const roleUsers = getUsersWithRole(role.id);
              return (
                <Card
                  key={role.id}
                  className={`cursor-pointer transition-all hover:shadow-lg ${selectedRole?.id === role.id ? "ring-2 ring-primary" : ""}`}
                  onClick={() => setSelectedRole(role)}
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Key className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{role.name}</CardTitle>
                          <CardDescription className="text-sm mt-1">{role.description}</CardDescription>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" size="icon">
                            <Settings className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditRole(role);
                            }}
                          >
                            <Edit className="w-4 h-4 mr-2" />
                            Edit Role
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCloneRole(role);
                            }}
                          >
                            <Copy className="w-4 h-4 mr-2" />
                            Clone Role
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteRole(role);
                            }}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete Role
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-500">Permissions</span>
                        <span className="font-medium">{role.permissions.length}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-500">Users with this role</span>
                        <Badge variant="secondary">{roleUsers.length} users</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {selectedRole && (
          <Card className="w-96">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Role Details</CardTitle>
                <Button variant="ghost" size="icon" onClick={() => handleEditRole(selectedRole)}>
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-sm text-slate-700 mb-2">Description</h3>
                <p className="text-sm text-slate-600">{selectedRole.description}</p>
              </div>

              <div>
                <h3 className="font-medium text-sm text-slate-700 mb-2">Permissions ({selectedRole.permissions.length})</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedRole.permissions.map((permission) => (
                    <Badge key={permission} variant="secondary" className="text-xs">
                      {permission.replace(/_/g, " ")}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-sm text-slate-700">Users ({getUsersWithRole(selectedRole.id).length})</h3>
                </div>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {getUsersWithRole(selectedRole.id).length > 0 ? (
                    getUsersWithRole(selectedRole.id).map((user) => (
                      <div key={user.id} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-slate-50">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <Users className="w-4 h-4 text-primary" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">{user.name}</p>
                          <p className="text-xs text-slate-500">{user.email}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-slate-500 text-center py-4">No users assigned</p>
                  )}
                </div>
              </div>

              <div className="pt-4 space-y-2">
                <Button className="w-full" variant="default" onClick={() => handleEditRole(selectedRole)}>
                  Edit Role
                </Button>
                <Button className="w-full" variant="outline" onClick={() => handleCloneRole(selectedRole)}>
                  Clone Role
                </Button>
                <Button className="w-full" variant="destructive" onClick={() => handleDeleteRole(selectedRole)}>
                  Delete Role
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <EnhancedRoleCreationWizard
        open={isCreationWizardOpen}
        onClose={() => {
          setIsCreationWizardOpen(false);
          setEditingRole(null);
        }}
        editingRole={editingRole}
        onComplete={handleRoleComplete}
      />
    </div>
  );
}
