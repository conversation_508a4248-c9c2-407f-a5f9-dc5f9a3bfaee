import { Plus, Users as UsersIcon } from 'lucide-react';
import Button from '../components/common/Button';
import UserTable from '../components/users/UserTable';

export default function Users() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-wb-gray-900 flex items-center">
            <UsersIcon className="w-7 h-7 mr-3 text-primary" />
            User Management
          </h1>
          <p className="text-wb-gray-600 mt-1">Manage user accounts, roles, and permissions across your organization.</p>
        </div>
        <Button variant="primary">
          <Plus className="w-4 h-4 mr-2" />
          Add User
        </Button>
      </div>

      <UserTable />
    </div>
  );
}