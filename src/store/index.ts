import { configureStore } from '@reduxjs/toolkit';
import usersReducer from './slices/usersSlice';
import rolesReducer from './slices/rolesSlice';
import groupsReducer from './slices/groupsSlice';
import accessRequestsReducer from './slices/accessRequestsSlice';
import auditLogsReducer from './slices/auditLogsSlice';
import applicationsReducer from './slices/applicationsSlice';
import propertiesReducer from './slices/propertiesSlice';
import propertyGroupsReducer from './slices/propertyGroupsSlice';
import roleApplicationAssignmentsReducer from './slices/roleApplicationAssignmentsSlice';
import applicationPagesReducer from './slices/applicationPagesSlice';
import { auditLoggerMiddleware } from './middleware/auditLogger';

export const store = configureStore({
  reducer: {
    users: usersReducer,
    roles: rolesReducer,
    groups: groupsReducer,
    accessRequests: accessRequestsReducer,
    auditLogs: auditLogsReducer,
    applications: applicationsReducer,
    properties: propertiesReducer,
    propertyGroups: propertyGroupsReducer,
    roleApplicationAssignments: roleApplicationAssignmentsReducer,
    applicationPages: applicationPagesReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(auditLoggerMiddleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;