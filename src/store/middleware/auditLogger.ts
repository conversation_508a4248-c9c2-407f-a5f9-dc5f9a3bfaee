import type { Middleware } from '@reduxjs/toolkit';
import { addLog } from '../slices/auditLogsSlice';

// Action types that should be logged
const LOGGED_ACTIONS = [
  'users/addUser',
  'users/updateUser',
  'users/deleteUser',
  'roles/addRole',
  'roles/updateRole',
  'roles/deleteRole',
  'groups/addGroup',
  'groups/updateGroup',
  'groups/deleteGroup',
  'groups/addUserToGroup',
  'groups/removeUserFromGroup',
  'accessRequests/updateRequestStatus',
  'properties/addProperty',
  'properties/updateProperty',
  'properties/deleteProperty',
];

// Map action types to readable descriptions
const ACTION_DESCRIPTIONS: Record<string, string> = {
  'users/addUser': 'CREATE_USER',
  'users/updateUser': 'UPDATE_USER',
  'users/deleteUser': 'DELETE_USER',
  'roles/addRole': 'CREATE_ROLE',
  'roles/updateRole': 'UPDATE_ROLE',
  'roles/deleteRole': 'DELETE_ROLE',
  'groups/addGroup': 'CREATE_GROUP',
  'groups/updateGroup': 'UPDATE_GROUP',
  'groups/deleteGroup': 'DELETE_GROUP',
  'groups/addUserToGroup': 'ADD_USER_TO_GROUP',
  'groups/removeUserFromGroup': 'REMOVE_USER_FROM_GROUP',
  'accessRequests/updateRequestStatus': 'UPDATE_ACCESS_REQUEST',
  'properties/addProperty': 'CREATE_PROPERTY',
  'properties/updateProperty': 'UPDATE_PROPERTY',
  'properties/deleteProperty': 'DELETE_PROPERTY',
};

// Determine the category based on action type
const getCategory = (actionType: string): 'auth' | 'permission' | 'user' | 'system' => {
  if (actionType.includes('users/')) return 'user';
  if (actionType.includes('roles/') || actionType.includes('accessRequests/')) return 'permission';
  if (actionType.includes('groups/')) return 'user';
  return 'system';
};

// Get resource name from action payload
const getResource = (actionType: string, payload: unknown): string => {
  const p = payload as Record<string, unknown>;
  if (actionType.includes('users/')) {
    return (p?.name as string) || (p?.id as string) || 'User';
  }
  if (actionType.includes('roles/')) {
    return (p?.name as string) || (p?.id as string) || 'Role';
  }
  if (actionType.includes('groups/')) {
    return (p?.name as string) || (p?.groupId as string) || (p?.id as string) || 'Group';
  }
  if (actionType.includes('accessRequests/')) {
    return `Request #${(p?.requestId as string) || (p?.id as string) || 'Unknown'}`;
  }
  if (actionType.includes('properties/')) {
    return (p?.name as string) || (p?.id as string) || 'Property';
  }
  return 'Resource';
};

// Generate details message
const getDetails = (actionType: string, payload: unknown): string => {
  const p = payload as Record<string, unknown>;
  const action = ACTION_DESCRIPTIONS[actionType] || actionType;
  
  switch (actionType) {
    case 'users/addUser':
      return `Created new user: ${p.name} (${p.email})`;
    case 'users/updateUser':
      return `Updated user: ${p.name}`;
    case 'users/deleteUser':
      return `Deleted user with ID: ${p}`;
    case 'roles/addRole':
      return `Created new role: ${p.name}`;
    case 'roles/updateRole':
      return `Updated role: ${p.name}`;
    case 'roles/deleteRole':
      return `Deleted role with ID: ${p}`;
    case 'groups/addGroup':
      return `Created new group: ${p.name}`;
    case 'groups/updateGroup':
      return `Updated group: ${p.name}`;
    case 'groups/deleteGroup':
      return `Deleted group with ID: ${p}`;
    case 'groups/addUserToGroup':
      return `Added user ${p.userId} to group ${p.groupId}`;
    case 'groups/removeUserFromGroup':
      return `Removed user ${p.userId} from group ${p.groupId}`;
    case 'accessRequests/updateRequestStatus':
      return `${p.status === 'approved' ? 'Approved' : 'Denied'} access request #${p.requestId}`;
    case 'properties/addProperty':
      return `Created new property: ${p.name}`;
    case 'properties/updateProperty':
      return `Updated property: ${p.name}`;
    case 'properties/deleteProperty':
      return `Deleted property with ID: ${p}`;
    default:
      return `Performed action: ${action}`;
  }
};

export const auditLoggerMiddleware: Middleware = (store) => (next) => (action: unknown) => {
  // Execute the action first
  const result = next(action);
  
  // Type guard for action
  const typedAction = action as { type: string; payload?: unknown };

  // Log the action if it's in our list
  if (LOGGED_ACTIONS.includes(typedAction.type)) {
    // Don't log audit log actions to prevent infinite loop
    if (!typedAction.type.startsWith('auditLogs/')) {
      const logEntry = {
        user: 'Current User', // In a real app, get from auth state
        action: ACTION_DESCRIPTIONS[typedAction.type] || typedAction.type,
        resource: getResource(typedAction.type, typedAction.payload),
        details: getDetails(typedAction.type, typedAction.payload),
        status: 'success' as const,
        ipAddress: '127.0.0.1', // In a real app, get from request context
        category: getCategory(typedAction.type),
      };

      store.dispatch(addLog(logEntry));
    }
  }

  return result;
};