import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { AccessRequest } from '@/types';
import type { RootState } from '../index';

interface AccessRequestsState {
  requests: AccessRequest[];
  loading: boolean;
  error: string | null;
}

const initialState: AccessRequestsState = {
  requests: [
    {
      id: '1',
      userId: '2',
      userName: '<PERSON>',
      userEmail: '<EMAIL>',
      requestedRole: 'pm_user',
      requestedApplications: ['prism'],
      requestedProperties: ['prop1'],
      reason: 'Need access to manage property portfolios',
      status: 'pending',
      requestDate: '2025-01-20T09:00:00Z',
      reviewedBy: undefined,
      reviewDate: undefined,
    },
  ],
  loading: false,
  error: null,
};

const accessRequestsSlice = createSlice({
  name: 'accessRequests',
  initialState,
  reducers: {
    addRequest: (state, action: PayloadAction<AccessRequest>) => {
      state.requests.push(action.payload);
    },
    updateRequestStatus: (state, action: PayloadAction<{
      requestId: string;
      status: 'approved' | 'denied';
      reviewedBy: string;
    }>) => {
      const request = state.requests.find(r => r.id === action.payload.requestId);
      if (request) {
        request.status = action.payload.status;
        request.reviewedBy = action.payload.reviewedBy;
        request.reviewDate = new Date().toISOString();
      }
    },
    deleteRequest: (state, action: PayloadAction<string>) => {
      state.requests = state.requests.filter(request => request.id !== action.payload);
    },
    setRequests: (state, action: PayloadAction<AccessRequest[]>) => {
      state.requests = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  addRequest,
  updateRequestStatus,
  deleteRequest,
  setRequests,
  setLoading,
  setError,
} = accessRequestsSlice.actions;

// Selectors
export const selectAllRequests = (state: RootState) => state.accessRequests.requests;
export const selectRequestById = (requestId: string) => (state: RootState) =>
  state.accessRequests.requests.find(request => request.id === requestId);
export const selectPendingRequests = (state: RootState) =>
  state.accessRequests.requests.filter(request => request.status === 'pending');
export const selectApprovedRequests = (state: RootState) =>
  state.accessRequests.requests.filter(request => request.status === 'approved');
export const selectDeniedRequests = (state: RootState) =>
  state.accessRequests.requests.filter(request => request.status === 'denied');
export const selectRequestsLoading = (state: RootState) => state.accessRequests.loading;
export const selectRequestsError = (state: RootState) => state.accessRequests.error;

export default accessRequestsSlice.reducer;