import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';
import { applicationPages as staticApplicationPages } from '@/data/permissions';

export interface ApplicationPage {
  id: string;
  name: string;
}

export interface ApplicationPagesState {
  pages: Record<string, ApplicationPage[]>;
}

const initialState: ApplicationPagesState = {
  pages: { ...staticApplicationPages },
};

const applicationPagesSlice = createSlice({
  name: 'applicationPages',
  initialState,
  reducers: {
    addPage: (
      state,
      action: PayloadAction<{ applicationId: string; page: ApplicationPage }>
    ) => {
      const { applicationId, page } = action.payload;
      if (!state.pages[applicationId]) {
        state.pages[applicationId] = [];
      }
      state.pages[applicationId].push(page);
    },
    updatePage: (
      state,
      action: PayloadAction<{
        applicationId: string;
        pageId: string;
        updates: Partial<ApplicationPage>;
      }>
    ) => {
      const { applicationId, pageId, updates } = action.payload;
      const pages = state.pages[applicationId];
      if (pages) {
        const pageIndex = pages.findIndex((p) => p.id === pageId);
        if (pageIndex !== -1) {
          pages[pageIndex] = { ...pages[pageIndex], ...updates };
        }
      }
    },
    deletePage: (
      state,
      action: PayloadAction<{ applicationId: string; pageId: string }>
    ) => {
      const { applicationId, pageId } = action.payload;
      const pages = state.pages[applicationId];
      if (pages) {
        state.pages[applicationId] = pages.filter((p) => p.id !== pageId);
      }
    },
    initializePages: (
      state,
      action: PayloadAction<Record<string, ApplicationPage[]>>
    ) => {
      state.pages = action.payload;
    },
  },
});

export const { addPage, updatePage, deletePage, initializePages } =
  applicationPagesSlice.actions;

export const selectAllApplicationPages = (state: RootState) =>
  state.applicationPages.pages;

export const selectPagesByApplicationId = (applicationId: string) => (state: RootState) =>
  state.applicationPages.pages[applicationId] || [];

export const selectPageExists = (applicationId: string, pageName: string) => (state: RootState) => {
  const pages = state.applicationPages.pages[applicationId] || [];
  return pages.some((page) => page.name.toLowerCase() === pageName.toLowerCase());
};

export const selectPageCount = (applicationId: string) => (state: RootState) => {
  const pages = state.applicationPages.pages[applicationId] || [];
  return pages.length;
};

export default applicationPagesSlice.reducer;