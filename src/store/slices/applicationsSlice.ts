import { createSlice } from '@reduxjs/toolkit';
import type { Application } from '@/types';
import type { RootState } from '../index';
import { applications as applicationsData } from '@/data/applications';

interface ApplicationsState {
  applications: Application[];
}

const initialState: ApplicationsState = {
  applications: applicationsData, // Keep existing applications as reference data
};

const applicationsSlice = createSlice({
  name: 'applications',
  initialState,
  reducers: {
    // Applications are typically read-only reference data
    // But we can add actions if needed in the future
  },
});

// Selectors
export const selectAllApplications = (state: RootState) => state.applications.applications;
export const selectApplicationById = (appId: string) => (state: RootState) =>
  state.applications.applications.find(app => app.id === appId);

export default applicationsSlice.reducer;