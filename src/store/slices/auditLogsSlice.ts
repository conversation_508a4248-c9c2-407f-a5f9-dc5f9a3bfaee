import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';

export interface AuditLog {
  id: string;
  timestamp: string;
  user: string;
  action: string;
  resource: string;
  details: string;
  status: 'success' | 'warning' | 'error';
  ipAddress: string;
  category: 'auth' | 'permission' | 'user' | 'system';
}

interface AuditLogsState {
  logs: AuditLog[];
  loading: boolean;
  error: string | null;
}

const initialState: AuditLogsState = {
  logs: [
    {
      id: '1',
      timestamp: new Date().toISOString(),
      user: 'System',
      action: 'SYSTEM_STARTUP',
      resource: 'Application',
      details: 'System initialized with Redux state management',
      status: 'success',
      ipAddress: '127.0.0.1',
      category: 'system',
    },
  ],
  loading: false,
  error: null,
};

const auditLogsSlice = createSlice({
  name: 'auditLogs',
  initialState,
  reducers: {
    addLog: (state, action: PayloadAction<Omit<AuditLog, 'id' | 'timestamp'>>) => {
      const newLog: AuditLog = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
      };
      state.logs.unshift(newLog); // Add to beginning for most recent first
      // Keep only last 1000 logs
      if (state.logs.length > 1000) {
        state.logs = state.logs.slice(0, 1000);
      }
    },
    clearLogs: (state) => {
      state.logs = [];
    },
    setLogs: (state, action: PayloadAction<AuditLog[]>) => {
      state.logs = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  addLog,
  clearLogs,
  setLogs,
  setLoading,
  setError,
} = auditLogsSlice.actions;

// Selectors
export const selectAllLogs = (state: RootState) => state.auditLogs.logs;
export const selectLogsByCategory = (category: AuditLog['category']) => (state: RootState) =>
  state.auditLogs.logs.filter(log => log.category === category);
export const selectRecentLogs = (limit: number = 10) => (state: RootState) =>
  state.auditLogs.logs.slice(0, limit);
export const selectLogsByUser = (user: string) => (state: RootState) =>
  state.auditLogs.logs.filter(log => log.user === user);
export const selectLogsByDateRange = (startDate: string, endDate: string) => (state: RootState) =>
  state.auditLogs.logs.filter(log => 
    log.timestamp >= startDate && log.timestamp <= endDate
  );
export const selectAuditLogsLoading = (state: RootState) => state.auditLogs.loading;
export const selectAuditLogsError = (state: RootState) => state.auditLogs.error;

export default auditLogsSlice.reducer;