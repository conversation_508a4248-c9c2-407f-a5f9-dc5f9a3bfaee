import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { Group } from "@/types";
import type { RootState } from "../index";

interface GroupsState {
  groups: Group[];
  loading: boolean;
  error: string | null;
}

const initialState: GroupsState = {
  groups: [
    {
      id: "grp-1",
      name: "Regional Property Managers",
      description: "Property managers responsible for regional operations",
      memberCount: 5,
      members: ["1", "3", "5", "7", "9"],
      permissions: ["manage_properties", "view_reports"],
      assignmentIds: ["assign_1", "assign_2"], // Prism MDP Admin + RAMI Security Admin
      applicationRoles: [], // Deprecated
    },
    {
      id: "grp-2",
      name: "System Administrators",
      description: "Full system administrators with complete access",
      memberCount: 2,
      members: ["2", "4"],
      permissions: ["full_system_access"],
      assignmentIds: ["assign_1", "assign_2", "assign_3", "assign_4"], // All assignments
      applicationRoles: [], // Deprecated
    },
    {
      id: "grp-3",
      name: "Accounting Team",
      description: "Accounting and finance team members",
      memberCount: 3,
      members: ["6", "8", "10"],
      permissions: ["view_financial_reports"],
      assignmentIds: ["assign_4"], // Labor Distribution Report Accounting User
      applicationRoles: [], // Deprecated
    },
  ],
  loading: false,
  error: null,
};

const groupsSlice = createSlice({
  name: "groups",
  initialState,
  reducers: {
    addGroup: (state, action: PayloadAction<Group>) => {
      state.groups.push(action.payload);
    },
    updateGroup: (state, action: PayloadAction<Group>) => {
      const index = state.groups.findIndex((group) => group.id === action.payload.id);
      if (index !== -1) {
        state.groups[index] = action.payload;
      }
    },
    deleteGroup: (state, action: PayloadAction<string>) => {
      state.groups = state.groups.filter((group) => group.id !== action.payload);
    },
    setGroups: (state, action: PayloadAction<Group[]>) => {
      state.groups = action.payload;
    },
    addUserToGroup: (state, action: PayloadAction<{ groupId: string; userId: string }>) => {
      const group = state.groups.find((g) => g.id === action.payload.groupId);
      if (group && !group.members.includes(action.payload.userId)) {
        group.members.push(action.payload.userId);
        group.memberCount = group.members.length;
      }
    },
    removeUserFromGroup: (state, action: PayloadAction<{ groupId: string; userId: string }>) => {
      const group = state.groups.find((g) => g.id === action.payload.groupId);
      if (group) {
        group.members = group.members.filter((id) => id !== action.payload.userId);
        group.memberCount = group.members.length;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { addGroup, updateGroup, deleteGroup, setGroups, addUserToGroup, removeUserFromGroup, setLoading, setError } = groupsSlice.actions;

// Selectors
export const selectAllGroups = (state: RootState) => state.groups.groups;
export const selectGroupById = (groupId: string) => (state: RootState) => state.groups.groups.find((group) => group.id === groupId);
export const selectGroupsLoading = (state: RootState) => state.groups.loading;
export const selectGroupsError = (state: RootState) => state.groups.error;
export const selectGroupsByUser = (userId: string) => (state: RootState) => state.groups.groups.filter((group) => group.members.includes(userId));

export default groupsSlice.reducer;
