import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { Property } from "@/types";
import type { RootState } from "../index";

interface PropertiesState {
  properties: Property[];
  loading: boolean;
  error: string | null;
}

const initialState: PropertiesState = {
  properties: [
    { id: "1", name: "1200M", region: "West", type: "Multi-family" },
    { id: "2", name: "1212 Lofts", region: "East", type: "Luxury Lofts" },
    { id: "3", name: "1325 Jefferson", region: "East", type: "Apartment Complex" },
    { id: "4", name: "1200 Broadway", region: "East", type: "Mixed-use" },
    { id: "5", name: "1940 Green Randolph Street Lofts", region: "West", type: "Urban Lofts" },
    { id: "6", name: "1407 On Michigan", region: "West", type: "High-rise" },
    { id: "7", name: "16 Twenty", region: "West", type: "Modern Apartments" },
    { id: "8", name: "1820 Lake", region: "West", type: "Lakefront Property" },
    { id: "9", name: "1808 Edgehill", region: "East", type: "Garden Apartments" },
    { id: "10", name: "180 North Jefferson", region: "East", type: "Downtown Tower" },
  ],
  loading: false,
  error: null,
};

const propertiesSlice = createSlice({
  name: "properties",
  initialState,
  reducers: {
    addProperty: (state, action: PayloadAction<Property>) => {
      state.properties.push(action.payload);
    },
    updateProperty: (state, action: PayloadAction<Property>) => {
      const index = state.properties.findIndex((prop) => prop.id === action.payload.id);
      if (index !== -1) {
        state.properties[index] = action.payload;
      }
    },
    deleteProperty: (state, action: PayloadAction<string>) => {
      state.properties = state.properties.filter((prop) => prop.id !== action.payload);
    },
    setProperties: (state, action: PayloadAction<Property[]>) => {
      state.properties = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { addProperty, updateProperty, deleteProperty, setProperties, setLoading, setError } = propertiesSlice.actions;

// Selectors
export const selectAllProperties = (state: RootState) => state.properties.properties;
export const selectPropertyById = (propId: string) => (state: RootState) => state.properties.properties.find((prop) => prop.id === propId);
export const selectPropertiesByRegion = (region: "East" | "West") => (state: RootState) =>
  state.properties.properties.filter((prop) => prop.region === region);
export const selectPropertiesLoading = (state: RootState) => state.properties.loading;
export const selectPropertiesError = (state: RootState) => state.properties.error;

export default propertiesSlice.reducer;
