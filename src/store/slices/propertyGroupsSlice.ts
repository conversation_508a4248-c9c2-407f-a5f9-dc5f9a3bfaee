import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { PropertyGroup } from "@/types";
import type { RootState } from "../index";

interface PropertyGroupsState {
  propertyGroups: PropertyGroup[];
  loading: boolean;
  error: string | null;
}

const initialState: PropertyGroupsState = {
  propertyGroups: [
    {
      id: "pg-east-1",
      name: "East Region Properties",
      description: "All properties located in the East region",
      properties: ["2", "3", "4", "9", "10", "11", "12", "15"],
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-15T10:00:00Z",
    },
    {
      id: "pg-west-1",
      name: "West Region Properties",
      description: "All properties located in the West region",
      properties: ["1", "5", "6", "7", "8", "13", "14"],
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
    },
    {
      id: "pg-luxury-1",
      name: "Luxury Portfolio",
      description: "High-end luxury properties across all regions",
      properties: ["2", "6", "14"],
      createdAt: "2024-01-16T09:00:00Z",
      updatedAt: "2024-01-16T09:00:00Z",
    },
    {
      id: "pg-downtown-1",
      name: "Downtown Properties",
      description: "Properties located in downtown areas",
      properties: ["4", "10", "12"],
      createdAt: "2024-01-16T14:00:00Z",
      updatedAt: "2024-01-16T14:00:00Z",
    },
  ],
  loading: false,
  error: null,
};

const propertyGroupsSlice = createSlice({
  name: "propertyGroups",
  initialState,
  reducers: {
    addPropertyGroup: (state, action: PayloadAction<PropertyGroup>) => {
      state.propertyGroups.push(action.payload);
    },
    updatePropertyGroup: (state, action: PayloadAction<PropertyGroup>) => {
      const index = state.propertyGroups.findIndex((group) => group.id === action.payload.id);
      if (index !== -1) {
        state.propertyGroups[index] = action.payload;
      }
    },
    deletePropertyGroup: (state, action: PayloadAction<string>) => {
      state.propertyGroups = state.propertyGroups.filter((group) => group.id !== action.payload);
    },
    setPropertyGroups: (state, action: PayloadAction<PropertyGroup[]>) => {
      state.propertyGroups = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { addPropertyGroup, updatePropertyGroup, deletePropertyGroup, setPropertyGroups, setLoading, setError } = propertyGroupsSlice.actions;

export const selectAllPropertyGroups = (state: RootState) => state.propertyGroups.propertyGroups;
export const selectPropertyGroupById = (groupId: string) => (state: RootState) =>
  state.propertyGroups.propertyGroups.find((group) => group.id === groupId);
export const selectPropertyGroupsLoading = (state: RootState) => state.propertyGroups.loading;
export const selectPropertyGroupsError = (state: RootState) => state.propertyGroups.error;

export default propertyGroupsSlice.reducer;
