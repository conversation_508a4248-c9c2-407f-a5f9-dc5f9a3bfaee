import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "../index";

export interface RoleApplicationAssignment {
  id: string;
  roleId: string;
  roleName: string;
  applicationId: string;
  applicationName: string;
  permissions: {
    [pageId: string]: string[]; // Array of operations: ["create", "read", "update", "delete", "execute"]
  };
  createdAt: string;
  modifiedAt: string;
}

interface RoleApplicationAssignmentsState {
  assignments: RoleApplicationAssignment[];
  loading: boolean;
  error: string | null;
}

// Sample initial assignments
const initialAssignments: RoleApplicationAssignment[] = [
  {
    id: "assign_1",
    roleId: "mdp_admin",
    roleName: "MDP Admin",
    applicationId: "prism",
    applicationName: "Prism",
    permissions: {
      dashboard: ["read"],
      properties: ["create", "read", "update", "delete"],
      tenants: ["create", "read", "update", "delete"],
      leases: ["create", "read", "update", "delete", "execute"],
      maintenance: ["create", "read", "update", "delete"],
      reports: ["read", "execute"],
      financials: ["read", "update"],
      documents: ["create", "read", "update", "delete"],
      settings: ["read", "update"],
    },
    createdAt: "2024-01-15",
    modifiedAt: "2024-01-20",
  },
  {
    id: "assign_2",
    roleId: "sec_admin",
    roleName: "Security Admin",
    applicationId: "rami",
    applicationName: "RAMI",
    permissions: {
      overview: ["read"],
      assets: ["read"],
      performance: ["read"],
      analytics: ["read", "execute"],
      compliance: ["read", "update"],
      risk_management: ["read"],
      portfolio: ["read"],
      transactions: ["read"],
    },
    createdAt: "2024-01-10",
    modifiedAt: "2024-01-18",
  },
  {
    id: "assign_3",
    roleId: "it_sup",
    roleName: "IT Super User",
    applicationId: "ems",
    applicationName: "Employee Management System",
    permissions: {
      employees: ["create", "read", "update", "delete"],
      departments: ["create", "read", "update", "delete"],
      roles: ["create", "read", "update", "delete"],
      onboarding: ["create", "read", "update", "execute"],
      performance: ["read", "update", "execute"],
      reports: ["read", "execute"],
      settings: ["create", "read", "update", "delete"],
    },
    createdAt: "2024-01-12",
    modifiedAt: "2024-01-22",
  },
  {
    id: "assign_4",
    roleId: "acct_user",
    roleName: "Accounting User",
    applicationId: "ldr",
    applicationName: "Labor Distribution Report",
    permissions: {
      reports: ["read", "execute"],
      analytics: ["read", "execute"],
      export: ["read", "execute"],
      schedules: ["read"],
      departments: ["read"],
      settings: ["read"],
    },
    createdAt: "2024-01-08",
    modifiedAt: "2024-01-25",
  },
];

const initialState: RoleApplicationAssignmentsState = {
  assignments: initialAssignments,
  loading: false,
  error: null,
};

const roleApplicationAssignmentsSlice = createSlice({
  name: "roleApplicationAssignments",
  initialState,
  reducers: {
    addAssignment: (state, action: PayloadAction<RoleApplicationAssignment>) => {
      state.assignments.push(action.payload);
    },
    updateAssignment: (state, action: PayloadAction<RoleApplicationAssignment>) => {
      const index = state.assignments.findIndex((a) => a.id === action.payload.id);
      if (index !== -1) {
        state.assignments[index] = action.payload;
      }
    },
    deleteAssignment: (state, action: PayloadAction<string>) => {
      state.assignments = state.assignments.filter((a) => a.id !== action.payload);
    },
    setAssignments: (state, action: PayloadAction<RoleApplicationAssignment[]>) => {
      state.assignments = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { 
  addAssignment, 
  updateAssignment, 
  deleteAssignment, 
  setAssignments, 
  setLoading, 
  setError 
} = roleApplicationAssignmentsSlice.actions;

// Selectors
export const selectAllAssignments = (state: RootState) => state.roleApplicationAssignments.assignments;
export const selectAssignmentById = (id: string) => (state: RootState) => 
  state.roleApplicationAssignments.assignments.find((a) => a.id === id);
export const selectAssignmentsLoading = (state: RootState) => state.roleApplicationAssignments.loading;
export const selectAssignmentsError = (state: RootState) => state.roleApplicationAssignments.error;

export default roleApplicationAssignmentsSlice.reducer;