import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { User } from '@/types';
import type { RootState } from '../index';
import { users as importedUsers } from '@/data/users';

interface UsersState {
  users: User[];
  loading: boolean;
  error: string | null;
}

const initialState: UsersState = {
  users: importedUsers,
  loading: false,
  error: null,
};

const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    addUser: (state, action: PayloadAction<User>) => {
      state.users.push(action.payload);
    },
    updateUser: (state, action: PayloadAction<User>) => {
      const index = state.users.findIndex(user => user.id === action.payload.id);
      if (index !== -1) {
        state.users[index] = action.payload;
      }
    },
    deleteUser: (state, action: PayloadAction<string>) => {
      state.users = state.users.filter(user => user.id !== action.payload);
    },
    setUsers: (state, action: PayloadAction<User[]>) => {
      state.users = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  addUser,
  updateUser,
  deleteUser,
  setUsers,
  setLoading,
  setError,
} = usersSlice.actions;

// Selectors
export const selectAllUsers = (state: RootState) => state.users.users;
export const selectUserById = (userId: string) => (state: RootState) =>
  state.users.users.find(user => user.id === userId);
export const selectUsersLoading = (state: RootState) => state.users.loading;
export const selectUsersError = (state: RootState) => state.users.error;
export const selectActiveUsers = (state: RootState) =>
  state.users.users.filter(user => user.status === 'active');
export const selectPendingUsers = (state: RootState) =>
  state.users.users.filter(user => user.status === 'pending');

export default usersSlice.reducer;