export interface User {
  id: string;
  name: string;
  email: string;
  status: "active" | "inactive" | "pending";
  currentRole: string | null; // Deprecated, use roles instead
  roles?: string[]; // Individual roles assigned directly
  groups?: string[]; // Groups the user belongs to
  lastLogin: string | null;
  requestDate?: string;
  applications: string[];
  properties: string[];
  propertyGroups?: string[];
  avatar?: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  applicationPermissions?: ApplicationPermission[];
}

export interface PagePermission {
  pageId: string;
  pageName: string;
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
}

export interface ApplicationPermission {
  applicationId: string;
  applicationName: string;
  pages: PagePermission[];
}

export interface Application {
  id: string;
  name: string;
  description: string;
}

export interface Property {
  id: string;
  name: string;
  region: "East" | "West";
  type: string;
}

export interface AccessRequest {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  requestedRole: string;
  requestedApplications: string[];
  requestedProperties: string[];
  reason: string;
  status: "pending" | "approved" | "denied";
  requestDate: string;
  reviewedBy?: string;
  reviewDate?: string;
}

export interface ApplicationRole {
  applicationId: string;
  applicationName: string;
  roleId: string;
  roleName: string;
}

export interface Group {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  members: string[];
  permissions: string[];
  applicationRoles?: ApplicationRole[]; // Deprecated - for backward compatibility
  assignmentIds: string[]; // IDs of RoleApplicationAssignments
}

export interface PropertyGroup {
  id: string;
  name: string;
  description: string;
  properties: string[];
  createdAt: string;
  updatedAt: string;
}
