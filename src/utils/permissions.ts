import { CRUD_OPERATIONS } from "@/constants/permissions";

export interface PermissionSummary {
  totalPermissions: number;
  configuredPages: number;
  byOperation: Record<string, number>;
}

export const calculatePermissionSummary = (
  pagePermissions: Array<{
    permissions: Record<string, string[]>;
  }>
): PermissionSummary => {
  let totalPermissions = 0;
  let configuredPages = 0;
  const byOperation: Record<string, number> = {};

  CRUD_OPERATIONS.forEach((op) => {
    byOperation[op] = 0;
  });

  pagePermissions.forEach((page) => {
    let pageHasPermissions = false;
    Object.values(page.permissions).forEach((permissions) => {
      if (permissions.length > 0) {
        pageHasPermissions = true;
        permissions.forEach((perm) => {
          if (perm in byOperation) {
            byOperation[perm]++;
            totalPermissions++;
          }
        });
      }
    });
    if (pageHasPermissions) configuredPages++;
  });

  return { totalPermissions, configuredPages, byOperation };
};

export const getPermissionCount = (permissions: Record<string, string[]>): number => {
  let count = 0;
  Object.values(permissions).forEach((perms) => {
    count += perms.length;
  });
  return count;
};

export const getInitials = (name: string): string => {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};